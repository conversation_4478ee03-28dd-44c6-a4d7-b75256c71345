package com.amobilab.ezmath.ai.utils

import amobi.module.common.CommApplication
import amobi.module.common.configs.PrefAssist
import android.app.Activity
import android.util.Log
import com.amobilab.ezmath.ai.utils.IapAssist.Products.COINS_PURCHASE_12000
import com.amobilab.ezmath.ai.utils.IapAssist.Products.COINS_PURCHASE_1500
import com.amobilab.ezmath.ai.utils.IapAssist.Products.COINS_PURCHASE_25000
import com.amobilab.ezmath.ai.utils.IapAssist.Products.COINS_PURCHASE_53000
import com.amobilab.ezmath.ai.utils.IapAssist.Products.COINS_PURCHASE_5500
import com.amobilab.ezmath.ai.utils.IapAssist.Products.COIN_PURCHASE
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClient.ConnectionState
import com.android.billingclient.api.BillingClient.ProductType
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ConsumeParams
import com.android.billingclient.api.PendingPurchasesParams
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.consumePurchase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class IapAssist private constructor() {

    object Products {
        const val COIN_PURCHASE = "coin_purchase"
        const val COINS_PURCHASE_1500 = "coins_purchase_1500"
        const val COINS_PURCHASE_5500 = "coins_purchase_5500"
        const val COINS_PURCHASE_12000 = "coins_purchase_12000"
        const val COINS_PURCHASE_25000 = "coins_purchase_25000"
        const val COINS_PURCHASE_53000 = "coins_purchase_53000"
    }

    companion object {
        val instance: IapAssist by lazy { HOLDER.INSTANCE }
        const val TAG = "IapAssist"

        private fun getDefaultAmount(productName: String): Long {
            return when (productName) {
                COIN_PURCHASE -> 0.99
                COINS_PURCHASE_1500 -> 1.99
                COINS_PURCHASE_5500 -> 6.99
                COINS_PURCHASE_12000 -> 15.99
                COINS_PURCHASE_25000 -> 31.99
                COINS_PURCHASE_53000 -> 54.99
                else -> 0.99
            }.toLong() * 1000000
        }

        private fun getDefaultFormattedPrice(productName: String): String {
            return when (productName) {
                COIN_PURCHASE -> "$0.99"
                COINS_PURCHASE_1500 -> "$1.99"
                COINS_PURCHASE_5500 -> "$6.99"
                COINS_PURCHASE_12000 -> "$15.99"
                COINS_PURCHASE_25000 -> "$31.99"
                COINS_PURCHASE_53000 -> "$54.99"
                else -> "$0.99"
            }
        }

        fun getIapProductDetails(productName: String): Triple<Long, String, String> {
            return Triple(
                PrefAssist.getLong("IAP_PRODUCT_AMOUNT_" + productName, getDefaultAmount(productName)),
                PrefAssist.getString("IAP_PRODUCT_FORMATTED_PRICE_" + productName, getDefaultFormattedPrice(productName)),
                PrefAssist.getString("IAP_PRODUCT_CURRENCY_CODE_" + productName, "$"),
            )
        }

        fun setIapProductDetails(
            productName: String,
            amount: Long,
            formattedPrice: String,
            currencyCode: String
        ) {
            PrefAssist.setLong("IAP_PRODUCT_AMOUNT_" + productName, amount)
            PrefAssist.setString("IAP_PRODUCT_FORMATTED_PRICE_" + productName, formattedPrice)
            PrefAssist.setString("IAP_PRODUCT_CURRENCY_CODE_" + productName, currencyCode)
        }
    }

    private object HOLDER {
        val INSTANCE = IapAssist()
    }

    private var billingConnectRetryCount = 0
    private var availableProducts: MutableList<ProductDetails> = mutableListOf()
    private var onConsumablePurchaseSuccess: ((purchases: List<Purchase>) -> Unit)? = null
    private var onConsumablePurchaseAlreadyOwned: ((purchases: List<Purchase>) -> Unit)? = null
    private var onConsumablePurchaseError: ((billingResult: BillingResult) -> Unit)? = null

    var isProductQuerying = false

    private val purchaseUpdateListener: PurchasesUpdatedListener by lazy {
        PurchasesUpdatedListener { billingResult, purchases ->
            // Handle purchase updates here
            when (billingResult.responseCode) {
                BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED -> {
                    if (!purchases.isNullOrEmpty()) {
                        onConsumablePurchaseAlreadyOwned?.invoke(purchases)
                        onConsumablePurchaseAlreadyOwned = null
                    }

                    onConsumablePurchaseSuccess = null
                    onConsumablePurchaseError = null
                }

                BillingClient.BillingResponseCode.OK -> {
                    if (!purchases.isNullOrEmpty()) {
                        onConsumablePurchaseSuccess?.invoke(purchases)
                        onConsumablePurchaseSuccess = null
                    }

                    onConsumablePurchaseError = null
                    onConsumablePurchaseAlreadyOwned = null
                }

                else -> {
                    onConsumablePurchaseError?.invoke(billingResult)
                    onConsumablePurchaseError = null

                    onConsumablePurchaseSuccess = null
                    onConsumablePurchaseAlreadyOwned = null
                }
            }

//            Log.d(TAG, "onPurchasesUpdated - billingResult: " + GsonUtils.toJson(billingResult))
//            Log.d(TAG, "onPurchasesUpdated - purchases: " + GsonUtils.toJson(billingResult))
        }
    }

    private val billingClient: BillingClient by lazy {
        BillingClient
            .newBuilder(CommApplication.appContext)
            .setListener(purchaseUpdateListener)
            .enablePendingPurchases(
                PendingPurchasesParams
                    .newBuilder()
                    .enableOneTimeProducts()
                    .build()
            )
            .build()
    }

    fun connect() {
        if (billingClient.connectionState != ConnectionState.CONNECTED)
            billingClient.startConnection(
                object : BillingClientStateListener {
                    override fun onBillingServiceDisconnected() {
                        if (billingConnectRetryCount <= 3) {
                            billingConnectRetryCount++
                            connect()
                        }

                        Log.d(TAG, "onBillingServiceDisconnected: $billingConnectRetryCount")
                    }

                    override fun onBillingSetupFinished(p0: BillingResult) {
//                        Log.d(TAG, "onBillingSetupFinished: ${GsonUtils.toJson(p0)}")

                        if (p0.responseCode == BillingClient.BillingResponseCode.OK) {
                            billingConnectRetryCount = 0
                            queryProducts()
                            return
                        }

                        if (billingConnectRetryCount <= 3) {
                            billingConnectRetryCount++
                            connect()
                        }
                    }
                }
            )
    }

    fun disconnect() {
        billingClient.endConnection()
    }

    fun getQueriedProduct(productId: String): ProductDetails? {
        return availableProducts.find { it.productId == productId }
    }

    fun queryProducts(
        onQuerySuccess: (List<ProductDetails>) -> Unit = {},
        onQueryFailed: (Int) -> Unit = {}
    ) {
        if (isProductQuerying)
            return

        isProductQuerying = true

        val productList = listOf(
            QueryProductDetailsParams.Product.newBuilder()
                .setProductId(COIN_PURCHASE)
                .setProductType(ProductType.INAPP)
                .build(),

            QueryProductDetailsParams.Product.newBuilder()
                .setProductId(COINS_PURCHASE_1500)
                .setProductType(ProductType.INAPP)
                .build(),

            QueryProductDetailsParams.Product.newBuilder()
                .setProductId(COINS_PURCHASE_5500)
                .setProductType(ProductType.INAPP)
                .build(),

            QueryProductDetailsParams.Product.newBuilder()
                .setProductId(COINS_PURCHASE_12000)
                .setProductType(ProductType.INAPP)
                .build(),

            QueryProductDetailsParams.Product.newBuilder()
                .setProductId(COINS_PURCHASE_25000)
                .setProductType(ProductType.INAPP)
                .build(),

            QueryProductDetailsParams.Product.newBuilder()
                .setProductId(COINS_PURCHASE_53000)
                .setProductType(ProductType.INAPP)
                .build()
        )
        val queryParams = QueryProductDetailsParams.newBuilder()
            .setProductList(productList)
            .build()

        billingClient.queryProductDetailsAsync(
            queryParams
        ) { billingResult, productDetailsList ->
//            Log.d(TAG, "onBillingSetupFinished - billingResult: ${GsonUtils.toJson(billingResult)}")
//            Log.d(TAG, "onBillingSetupFinished - productDetailsList: ${GsonUtils.toJson(productDetailsList)}")
            for (product in productDetailsList) {
                val purchaseDetails = product.oneTimePurchaseOfferDetails ?: continue
                setIapProductDetails(
                    product.productId,
                    purchaseDetails.priceAmountMicros,
                    purchaseDetails.formattedPrice,
                    purchaseDetails.priceCurrencyCode,
                )
            }


            isProductQuerying = false
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                availableProducts.clear()
                availableProducts.addAll(productDetailsList)
                onQuerySuccess(productDetailsList)
                return@queryProductDetailsAsync
            }
            onQueryFailed(billingResult.responseCode)
        }
    }

    fun purchaseConsumableProduct(
        activity: Activity,
        productId: String,
        onSuccess: () -> Unit = {},
        onError: (BillingResult) -> Unit = {}
    ) {
        val productDetails = getQueriedProduct(productId) ?: return
        val productParams = BillingFlowParams.ProductDetailsParams.newBuilder()
            .setProductDetails(productDetails)
            .build()
        val billingFlowParams = BillingFlowParams.newBuilder()
            .setProductDetailsParamsList(
                listOf(
                    productParams
                )
            )
            .build()
        onConsumablePurchaseSuccess = { purchases: List<Purchase> ->
            CoroutineScope(Dispatchers.IO).launch {
                for (purchase in purchases) {
                    val consumeParams = ConsumeParams.newBuilder()
                        .setPurchaseToken(purchase.purchaseToken)
                        .build()

                    billingClient.consumePurchase(consumeParams)
                }
                withContext(Dispatchers.Main) {
                    onSuccess.invoke()
                }
            }
        }

        onConsumablePurchaseAlreadyOwned = { purchases: List<Purchase> ->
            CoroutineScope(Dispatchers.IO).launch {
                for (purchase in purchases) {
                    val consumeParams = ConsumeParams.newBuilder()
                        .setPurchaseToken(purchase.purchaseToken)
                        .build()

                    billingClient.consumePurchase(consumeParams)
                }
                withContext(Dispatchers.Main) {
                    onSuccess.invoke()
                }
            }
        }

        onConsumablePurchaseError = { billingResult: BillingResult ->
            onError.invoke(billingResult)
        }

        billingClient.launchBillingFlow(activity, billingFlowParams)
    }
}