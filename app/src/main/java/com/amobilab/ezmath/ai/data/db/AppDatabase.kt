package com.amobilab.ezmath.ai.data.db

import android.content.Context
import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.amobilab.ezmath.ai.data.db.dao.ChatDao
import com.amobilab.ezmath.ai.data.db.dao.CoinHistoryDao
import com.amobilab.ezmath.ai.data.db.dao.HistoryDao
import com.amobilab.ezmath.ai.data.db.entities.ChatEntity
import com.amobilab.ezmath.ai.data.db.entities.CoinHistoryEntity
import com.amobilab.ezmath.ai.data.db.entities.HistoryEntity

class AppDatabase private constructor() {
    val MIGRATION_2_3 = object : Migration(2, 3) {
        override fun migrate(database: SupportSQLiteDatabase) {
            database.execSQL(
                "ALTER TABLE history_table ADD COLUMN modelAiChat TEXT NOT NULL DEFAULT ''"
            )
        }
    }


    @Database(
        entities = [
            ChatEntity::class,
            HistoryEntity::class,
            CoinHistoryEntity::class,
        ],
        version = 3,
        exportSchema = true,        
        autoMigrations = [
            AutoMigration(from = 1, to = 2),
        ]
    )

    abstract class AppRoomDatabase : RoomDatabase() {
        abstract fun historyDao(): HistoryDao
        abstract fun chatDao(): ChatDao
        abstract fun coinHistoryDao(): CoinHistoryDao
    }

    private object Holder {
        val instance = AppDatabase()
    }

    companion object {
        @JvmStatic
        @Synchronized
        fun getInstance() = Holder.instance

        const val DB_NAME = "app_db"
    }

    private lateinit var db: AppRoomDatabase

    fun init(context: Context) {
        if (!::db.isInitialized)
            db = Room.databaseBuilder(context, AppRoomDatabase::class.java, DB_NAME)
                .addMigrations(MIGRATION_2_3)
                .allowMainThreadQueries()
                .build()
    }

    fun getDatabase() = db
}