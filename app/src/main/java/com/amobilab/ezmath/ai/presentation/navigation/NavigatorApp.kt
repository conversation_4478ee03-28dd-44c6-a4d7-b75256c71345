package com.amobilab.ezmath.ai.presentation.navigation

import amobi.module.common.views.CommActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.NavHostController
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@ActivityRetainedScoped
class AppNavigator @Inject constructor() {

    private val _navEvents = Channel<Direction>(Channel.UNLIMITED)
    val navEvents: Flow<Direction> = _navEvents.receiveAsFlow()

    fun navigateTo(appNavScreen: ScreenRoutes) {
        _navEvents.trySend(Direction.NavigateTo(appNavScreen))
    }

    fun navigateBack() {
        _navEvents.trySend(Direction.NavigateBack)
    }

    sealed interface Direction {
        data class NavigateTo(
            val navDirections: ScreenRoutes,
        ) : Direction

        object NavigateBack : Direction
    }
}

fun AppNavigator.setupWithNavController(
    activity: CommActivity, navController: NavHostController
) {
    activity.lifecycleScope.launch {
        activity.repeatOnLifecycle(Lifecycle.State.STARTED) {
            navEvents.collect { direction ->
                when (direction) {
                    is AppNavigator.Direction.NavigateTo -> {
                        navController.navigate(direction.navDirections)
                    }

                    is AppNavigator.Direction.NavigateBack -> {
                        if (!navController.popBackStack()) {
                            activity.finish()
                        }
                    }
                }
            }
        }
    }
}