package com.amobilab.ezmath.ai.presentation.ui.home

import amobi.module.common.configs.CommFigs
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.foundation.AppTextAutoSize
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import android.annotation.SuppressLint
import android.app.Activity
import androidx.activity.compose.BackHandler
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.size
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.share_dialog.CommonDialog
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel
import com.amobilab.ezmath.ai.presentation.navigation.MainEntryActivity
import com.amobilab.ezmath.ai.presentation.navigation.ScreenRoutes
import com.amobilab.ezmath.ai.presentation.ui.home.tabs.ChatTabCompose
import com.amobilab.ezmath.ai.presentation.ui.home.tabs.HistoryChatTabCompose
import com.amobilab.ezmath.ai.presentation.ui.home.tabs.ScanTabCompose
import com.amobilab.ezmath.ai.presentation.ui.home.tabs.SettingsTabCompose
import com.amobilab.ezmath.ai.presentation.ui.home.values.BottomBarScreenRoutes

@Composable
@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter", "CoroutineCreationDuringComposition")
fun HomeCompose() {
    val context = LocalContext.current

    val navigatorViewModel: NavigatorViewModel? = if (PreviewAssist.IS_PREVIEW)
        null
    else
        hiltViewModel(LocalContext.current as MainEntryActivity)

    val navController = rememberNavController()

    val backStackEntryState by navController.currentBackStackEntryAsState()
    val currentRouteName = backStackEntryState
        ?.destination
        ?.route
        ?.substringAfterLast(".")
        ?.substringBefore("/")
        ?.split("?")
        ?.first()


    val borderColor = AppColors.current.divider2
    var isBottomBarVisible by remember { mutableStateOf(true) }

    val renderBottomBar = @Composable {
        if (isBottomBarVisible) {
            NavigationBar(
                modifier = Modifier
                    .drawBehind {
                        drawLine(
                            color = borderColor,
                            start = Offset(0f, 0f),
                            end = Offset(size.width, 0f),
                            strokeWidth = 2.dp.toPx()
                        )
                    },
                containerColor = AppColors.current.homeBottomNavBar,
            ) {
                val tabScreens = listOf(
                    BottomBarScreenRoutes.AiChat,
                    BottomBarScreenRoutes.Scan,
                    BottomBarScreenRoutes.History,
                    BottomBarScreenRoutes.Settings
                )
                tabScreens.forEach { currentTab ->
                    NavigationBarItem(
                        selected = currentRouteName == currentTab.screenRoute.route,
                        onClick = {
                            if (currentRouteName != currentTab.screenRoute.route) {
                                navController.navigate(currentTab.screenRoute) {
                                    popUpTo(0) {
                                        inclusive = true
                                    }
                                    launchSingleTop = true
                                }
                            }
                        },
                        icon = {
                            Image(
                                painter = painterResource(
                                    id = if (currentRouteName == currentTab.screenRoute.route)
                                        currentTab.iconActive
                                    else
                                        currentTab.iconInactive,
                                ),
                                modifier = Modifier
                                    .size(24.dp),
                                contentDescription = null,
                            )
                        },
                        label = {
                            AppTextAutoSize(
                                text = stringResource(currentTab.titleId),
                                color = if (currentRouteName == currentTab.screenRoute.route)
                                    AppColors.current.homeBottomNavBarActiveTint
                                else
                                    AppColors.current.homeBottomNavBarInactiveTint,
                                fontSize = AppFontSize.BODY2,
                                maxLines = 1,
                                fontWeight = if (currentRouteName == currentTab.screenRoute.route)
                                    FontWeight.W500
                                else
                                    FontWeight.W400,
                                textAlign = TextAlign.Center,
                            )
                        },
                        colors = NavigationBarItemDefaults.colors(
                            indicatorColor = Color.Transparent
                        )
                    )
                }
            }
        }
    }

    @Composable
    fun renderNavHost(innerPadding: PaddingValues) {
        val startRouteDestination = if (CommFigs.IS_SHOW_TEST_OPTION)
        // tssst
//            ScreenRoutes.AiChatTab()
//            ScreenRoutes.SettingTab()
//            ScreenRoutes.HistoryTab()
            ScreenRoutes.AiChatTab()
        else
            ScreenRoutes.AiChatTab()

        NavHost(
            navController = navController,
            startDestination = startRouteDestination,
            enterTransition = { EnterTransition.None },
            exitTransition = { ExitTransition.None }
        ) {
            composable<ScreenRoutes.ScanTab> {
                ScanTabCompose(
                    innerPadding,
                ) { scanMode, urlBitmap, isFromCamera, rectF ->
                    navigatorViewModel?.navigateTo(
                        ScreenRoutes.ScanDetail(
                            scanMode = scanMode,
                            url = urlBitmap,
                            isFromCamera = isFromCamera,
                            rectFLeft = rectF.left,
                            rectFTop = rectF.top,
                            rectFRight = rectF.right,
                            rectFBottom = rectF.bottom
                        )
                    )
                }
            }

            composable<ScreenRoutes.AiChatTab> {
                ChatTabCompose(
                    innerPaddingHome = innerPadding,
                    nextMode = {
                        navController.popBackStack(
                            startRouteDestination,
                            false
                        )
                        navigatorViewModel?.navigateTo(
                            ScreenRoutes.ChatScreen(
                                mode = it
                            )
                        )
                    },
                    onSend = {_mode, prompt,imgUrl ->
                        navigatorViewModel?.navigateTo(
                            ScreenRoutes.ChatScreen(
                                mode = _mode ,
                                ask = prompt,
                                urlBitmap = imgUrl
                            )
                        )
                    }
                )
            }

            composable<ScreenRoutes.HistoryTab> {
                HistoryChatTabCompose(
                    innerPaddingHome = innerPadding,
                    onClick = {
                        navController.popBackStack(
                            startRouteDestination,
                            false
                        )
                        navigatorViewModel?.navigateTo(
                            ScreenRoutes.ChatScreen(
                                idHistory = it,
                                urlBitmap = "",
                                ask = "",
                                mode = ChatQuestionMode.Default
                            )
                        )
                    },
                    onOffBottomBar = {
                        isBottomBarVisible = it
                    }
                )
            }

            composable<ScreenRoutes.SettingTab> {
                SettingsTabCompose(innerPadding)
            }
        }
    }

    Scaffold(
        bottomBar = { renderBottomBar() }
    ) { innerPadding ->
        renderNavHost(innerPadding)
    }

    var showExitDialog by remember { mutableStateOf(false) }
    BackHandler(true) {
        isBottomBarVisible = true
        if (currentRouteName != ScreenRoutes.AiChatTab().route) {
            navController.navigate(ScreenRoutes.AiChatTab()) {
                popUpTo(0) {
                    inclusive = true
                }
                launchSingleTop = true
            }
        } else {
            showExitDialog = true
        }
    }

    // Hiển thị dialog xác nhận thoát
    if (showExitDialog) {
        CommonDialog(
            title = stringResource(R.string.exit_app),
            message = stringResource(R.string.do_you_want_to_leave_now_don_t_forget_to_come_back_i_ll_be_waiting_for_you),
            confirmText = stringResource(R.string.txtid_ok),
            dismissText = stringResource(R.string.txtid_no),
            onConfirm = {(context as? Activity)?.finish() },
            onDismiss = {
                showExitDialog = false
            }
        )
    }
}
