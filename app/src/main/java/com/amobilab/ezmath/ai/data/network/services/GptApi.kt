package com.amobilab.ezmath.ai.data.network.services

import amobi.module.common.CommApplication
import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import amobi.module.openai.api.chat.ChatCompletion
import amobi.module.openai.api.chat.ChatCompletionRequest
import amobi.module.openai.api.chat.ChatMessage
import amobi.module.openai.api.chat.ChatRole
import amobi.module.openai.api.chat.ImagePart
import amobi.module.openai.api.chat.StreamOptions
import amobi.module.openai.api.chat.TextPart
import amobi.module.openai.api.model.ModelId
import amobi.module.openai.client.OpenAI
import amobi.module.openai.client.OpenAIHost
import amobi.module.otp.OtpHelper
import android.content.Context
import android.graphics.Bitmap
import android.util.Base64
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.configs.AppSecret
import com.amobilab.ezmath.ai.data.models.Chat
import com.amobilab.ezmath.ai.data.network.models.ChatResponse
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.BotType
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.values.Const
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.takeWhile
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream

class GptApi {
    companion object {
        // const val URL = "https://api.keyai.shop/v1/"
        // const val URL = "https://api.openai.com/v1"
        private const val URL_NO_STREAM = "https://us-central1-ezmath-ia.cloudfunctions.net/chatGptProxy/chatGptProxy"
        private const val URL_STREAM = "https://us-central1-ezmath-ia.cloudfunctions.net/chatGptProxyStream/chatGptProxyStream"
    }

    private val isCollecting = MutableStateFlow(true)

    fun stopCollecting() {
        isCollecting.value = false
    }

    suspend fun getResponsesNoStream(prompts: String, bitmap: Bitmap? = null, mode: ChatQuestionMode): ChatResponse {
        debugLog("AI Get Response: $mode")

        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        val openAI = OpenAI(
            token = "sk-xxx",
            host = OpenAIHost(baseUrl = URL_NO_STREAM),
            headers = mutableMapOf(
                "AppCheck-Token" to OtpHelper.getOTP(CommApplication.appContext),
            ).apply {
                if (AppSecret.IS_DEV)
                    put("IsDev", "true")
            }
        )

        return try {
            val messages = mutableListOf(
                ChatMessage(
                    role = ChatRole.System,
                    content = systemInstruction
                )
            )

            // Add user message with image if bitmap is provided
            if (bitmap != null) {
                messages.add(
                    ChatMessage(
                        role = ChatRole.User,
                        content = listOf(
                            TextPart(text = prompts),
                            ImagePart(url = "data:image/png;base64," + encodeToBase64(bitmap), detail = "low")
                        )
                    )
                )
            } else {
                messages.add(
                    ChatMessage(
                        role = ChatRole.User,
                        content = prompts
                    )
                )
            }

            val chatCompletionRequest = ChatCompletionRequest(
                model = ModelId(Const.AiModelName.GPT),
                messages = messages
            )

            val completion: ChatCompletion = openAI.chatCompletion(chatCompletionRequest)

            val content = completion.choices.firstOrNull()?.message?.content ?: ""
            val promptTokens = completion.usage?.promptTokens ?: 0
            val completionTokens = completion.usage?.completionTokens ?: 0
            val totalTokens = completion.usage?.totalTokens ?: 0

            ChatResponse(
                chat = Chat(
                    prompt = content,
                    bitmap = null,
                    isFromUser = false,
                    isError = false,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = promptTokens,
                outputTokenCount = completionTokens,
                totalTokenCount = totalTokens
            )
        } catch (e: Exception) {
            debugLog(" ${e.message}")
            ChatResponse(
                chat = Chat(
                    prompt = "",
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        }
    }

    fun getResponseWithImage(context: Context, prompt: String, bitmap: Bitmap, mode: ChatQuestionMode): Flow<ChatResponse> = flow {
        debugLog("AI Get Response: $mode")

        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        val openAI = OpenAI(
            "sk-xxx",
            host = OpenAIHost(baseUrl = URL_STREAM),
            headers = mutableMapOf(
                "AppCheck-Token" to OtpHelper.getOTP(context),
            ).apply {
                if (AppSecret.IS_DEV)
                    put("IsDev", "true")
            }
        )
        var isSentError = true
        try {
            val promptImage = when (mode) {
                ChatQuestionMode.Translate ->
                    context.getString(mode.promptImageId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

                else -> context.getString(mode.promptImageId)
            }

            val chatCompletionRequest = withContext(Dispatchers.IO) {
                val inputContent = listOf(
                    ChatMessage(
                        role = ChatRole.System,
                        content = systemInstruction,
                    ),
                    ChatMessage(
                        role = ChatRole.User,
                        content = listOf(
                            TextPart(text = "$promptImage: $prompt"),
                            ImagePart(url = "data:image/png;base64," + encodeToBase64(bitmap), detail = "low")
                        )
                    )
                )

                ChatCompletionRequest(
                    model = ModelId(Const.AiModelName.GPT),
                    streamOptions = StreamOptions(includeUsage = true),
                    messages = inputContent,
                    maxTokens = Const.MAX_OUTPUT_TOKENS,
                )
            }

            openAI.chatCompletions(chatCompletionRequest).collect { response ->
                val content = response.choices.firstOrNull()?.delta?.content
                if (content != null) {
                    isSentError = false
                }

                emit(
                    ChatResponse(
                        chat = Chat(
                            prompt = content ?: "",
                            bitmap = null,
                            isFromUser = false,
                            isError = false,
                            botType = BotType.BOT_GPT
                        ),
                        inputTokenCount = response.usage?.promptTokens ?: 0,
                        outputTokenCount = response.usage?.completionTokens ?: 0,
                        totalTokenCount = response.usage?.totalTokens ?: 0
                    )
                )
            }

        } catch (e: Exception) {
            debugLog("Response error: ${e.message}")
            emit(
                ChatResponse(
                    chat = Chat(
                        prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                        bitmap = null,
                        isFromUser = false,
                        isError = true,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = 0,
                    outputTokenCount = 0,
                    totalTokenCount = 0
                )
            )
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
        }
    }.catch { e ->
        debugLog("Response error: ${e.message}")
        emit(
            ChatResponse(
                chat = Chat(
                    prompt = context.getString(R.string.errors_please_try_again),
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        )
        isCollecting.value = false
        withContext(Dispatchers.Main) {
            MixedUtils.showToast(context, R.string.errors_please_try_again)
        }
    }

    fun getResponses(context: Context, listChat: MutableList<Chat>, mode: ChatQuestionMode): Flow<ChatResponse?> = flow {
        debugLog("AI Get Response: $mode")

        val openAI = OpenAI(
            "sk-xxx",
            host = OpenAIHost(baseUrl = URL_STREAM),
            headers = mutableMapOf(
                "AppCheck-Token" to OtpHelper.getOTP(context),
            ).apply {
                if (AppSecret.IS_DEV)
                    put("IsDev", "true")
            }
        )
        isCollecting.value = true
        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId) + ".\n" + "Returns the formula in KaTeX-compatible format"
        }
        var isSentError = true
        try {
            val response = withContext(Dispatchers.IO) {
                ChatCompletionRequest(
                    model = ModelId(Const.AiModelName.GPT),
                    streamOptions = StreamOptions(includeUsage = true),
                    maxTokens = Const.MAX_OUTPUT_TOKENS,
                    messages = listOf(
                        ChatMessage(
                            role = ChatRole.System,
                            content = systemInstruction
                        )
                    ) + listChat.take(6).reversed().map { item ->
                        if (item.isFromUser) {
                            if (item.bitmap != null) {
                                ChatMessage(
                                    role = ChatRole.User,
                                    content = listOf(
                                        TextPart(text = item.prompt),
                                        ImagePart(url = "data:image/png;base64," + encodeToBase64(item.bitmap), detail = "low")
                                    ),
                                )
                            } else {
                                ChatMessage(
                                    role = ChatRole.User,
                                    content = item.prompt
                                )
                            }
                        } else {
                            ChatMessage(
                                role = ChatRole.Assistant,
                                content = item.prompt
                            )
                        }
                    },
                )
            }

            openAI.chatCompletions(response)
                .takeWhile { isCollecting.value }
                .onCompletion {
                    debugLog("onCompletion : xong")
                }
                .collect { response ->

                    val content = response.choices.firstOrNull()?.delta?.content
                    if (content != null) {
                        isSentError = false
                    }
                    emit(
                        ChatResponse(
                            chat = Chat(
                                prompt = content ?: "",
                                bitmap = null,
                                isFromUser = false,
                                isError = false,
                                botType = BotType.BOT_GPT
                            ),
                            inputTokenCount = response.usage?.promptTokens ?: 0,
                            outputTokenCount = response.usage?.completionTokens ?: 0,
                            totalTokenCount = response.usage?.totalTokens ?: 0
                        )
                    )
                }
        } catch (e: Exception) {
            debugLog("Response error: ${e.message}")
            try {
                emit(
                    ChatResponse(
                        chat = Chat(
                            prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                            bitmap = null,
                            isFromUser = false,
                            isError = true,
                            botType = BotType.BOT_GPT
                        ),
                        inputTokenCount = 0,
                        outputTokenCount = 0,
                        totalTokenCount = 0
                    )
                )
            } catch (e: Exception) {
                debugLog("Response error: ${e.message}")
            }
            isCollecting.value = false
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
        }
    }.catch { e ->
        debugLog("Response error: ${e.message}")
        try {
            emit(
                ChatResponse(
                    chat = Chat(
                        prompt = context.getString(R.string.errors_please_try_again),
                        bitmap = null,
                        isFromUser = false,
                        isError = true,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = 0,
                    outputTokenCount = 0,
                    totalTokenCount = 0
                )
            )
        } catch (e: Exception) {
            debugLog("Response error: ${e.message}")
        }
        isCollecting.value = false
        withContext(Dispatchers.Main) {
            MixedUtils.showToast(context, R.string.errors_please_try_again)
        }
    }

    private fun encodeToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream)
        return Base64.encodeToString(byteArrayOutputStream.toByteArray(), Base64.NO_WRAP)
    }
}