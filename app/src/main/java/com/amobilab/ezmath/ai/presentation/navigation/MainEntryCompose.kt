package com.amobilab.ezmath.ai.presentation.navigation

import amobi.module.common.advertisements.reward_ad.AdvertsManagerReward
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import amobi.module.common.views.CommActivity
import amobi.module.compose.extentions.clipCorner
import amobi.module.compose.extentions.conditional
import amobi.module.compose.extentions.minSize
import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppBoxCentered
import amobi.module.compose.foundation.AppButton
import amobi.module.compose.foundation.AppButtonText
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRowCentered
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppSize
import android.annotation.SuppressLint
import android.graphics.RectF
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import androidx.navigation.toRoute
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.admob.AdvertsIdsMangerRewardAd
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainViewModel
import com.amobilab.ezmath.ai.presentation.ui.authentication.AuthScreen
import com.amobilab.ezmath.ai.presentation.ui.calculator.CalculatorCompose
import com.amobilab.ezmath.ai.presentation.ui.chat_bot.ChatBotCompose
import com.amobilab.ezmath.ai.presentation.ui.coin_history.CoinTransactionHistoryScreen
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.LiteratureScreen
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.ResearchAndAnalysisScreen
import com.amobilab.ezmath.ai.presentation.ui.feature_screen.WriteAnEssayScreen
import com.amobilab.ezmath.ai.presentation.ui.home.HomeCompose
import com.amobilab.ezmath.ai.presentation.ui.iap.IapCompose
import com.amobilab.ezmath.ai.presentation.ui.scan.ScanEditView
import com.amobilab.ezmath.ai.presentation.ui.scan.ScanViewModel
import com.amobilab.ezmath.ai.presentation.ui.screen_feedback.FeedbackSuggestionScreenCompose
import com.amobilab.ezmath.ai.presentation.ui.set_theme.SetThemeCompose
import com.amobilab.ezmath.ai.presentation.ui.user_info.UserDeleteScreen
import com.amobilab.ezmath.ai.utils.FirestoreUtils
import com.amobilab.ezmath.ai.utils.GoogleAuthUiClient
import com.google.android.gms.auth.api.identity.Identity
import java.util.Locale

@Composable
@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter", "CoroutineCreationDuringComposition")
fun MainEntryCompose(appNavigator: AppNavigator) {
    val context = LocalContext.current
    val viewModel = hiltViewModel<MainViewModel>()

    val coinTotal = viewModel.coinViewModel.coinTotal.collectAsState().value
    val coinTotalOld = remember { mutableLongStateOf(coinTotal) }

    val freeChat = viewModel.coinViewModel.freeChat.collectAsState().value

    val googleAuthUiClient by lazy {
        GoogleAuthUiClient(
            oneTapClient = Identity.getSignInClient(context),
            viewModel = viewModel
        )
    }

    val navController = rememberNavController()
    appNavigator.setupWithNavController(
        activity = LocalContext.current as MainEntryActivity,
        navController = navController
    )

    LaunchedEffect(freeChat) {
        FirestoreUtils.updateFreeChatUsage(freeChat)
    }

    LaunchedEffect(coinTotal) {
        if (!googleAuthUiClient.checkAccountValidity()) {
            googleAuthUiClient.signOut()
        }
        if (coinTotal == coinTotalOld.longValue) return@LaunchedEffect
        googleAuthUiClient.getSignedInUser()?.let {
            if (coinTotal == 0L) return@let
            FirestoreUtils.updateCoinsForUser(it.userId, coinTotal)
        }

        val difference = coinTotal - coinTotalOld.longValue
        val message = if (difference > 0) "+$difference" else difference.toString()
        if (!CommFigs.IS_PRODUCT && difference < 0)
            MixedUtils.showToast(context, context.getString(R.string.txtid_coins) + ": $message")

        coinTotalOld.longValue = coinTotal
    }

    val viewModelScan = hiltViewModel<ScanViewModel>()

    val backStackEntryState by navController.currentBackStackEntryAsState()
    val currentRouteName = backStackEntryState
        ?.destination
        ?.route
        ?.substringAfterLast(".")
        ?.substringBefore("/")
        ?.split("?")
        ?.first()

    @Composable
    fun renderNavHost(innerPadding: PaddingValues) {
        NavHost(
            navController = navController,
            startDestination = ScreenRoutes.HomeScreen()
        ) {
            composable<ScreenRoutes.HomeScreen> {
                HomeCompose()
            }

            composable<ScreenRoutes.ScanDetail> { navBackStackEntry ->
                val parameters = navBackStackEntry.toRoute<ScreenRoutes.ScanDetail>()
                val rectFCustom = RectF(
                    parameters.rectFLeft, parameters.rectFTop,
                    parameters.rectFRight, parameters.rectFBottom
                )
                ScanEditView(
                    viewModel = viewModelScan,
                    coinViewModel = viewModel.coinViewModel,
                    scanMode = parameters.scanMode,
                    urlBitmap = parameters.url,
                    isFromCamera = parameters.isFromCamera,
                    rectF = rectFCustom,
                ) { urlImage, ask ->
                    debugLog(" ${parameters.scanMode.id}")
                    debugLog(" ask $ask")

                    navController.navigate(
                        ScreenRoutes.ChatScreen(
                            urlBitmap = urlImage,
                            ask = ask,
                            mode = parameters.scanMode
                        )
                    ) {
                        popUpTo(navController.graph.startDestinationId)
                    }
                }
            }
            composable<ScreenRoutes.Calculator> {
                CalculatorCompose()
            }

            composable<ScreenRoutes.InAppPurchaseRoute> {
                IapCompose()
            }

            composable<ScreenRoutes.CoinHistory> {
                CoinTransactionHistoryScreen(innerPadding) {
                    navController.navigateUp()
                }
            }

            composable<ScreenRoutes.SignIn> {
                AuthScreen()
            }

            composable<ScreenRoutes.SetTheme> {
                SetThemeCompose(){
                    navController.navigateUp()
                }
            }

            composable<ScreenRoutes.FeedbackSuggestion> {
                FeedbackSuggestionScreenCompose()
            }

            composable<ScreenRoutes.Literature> {
                LiteratureScreen(
                    onSend = {_mode, prompt,imgUrl ->
                        navController.navigate(
                            ScreenRoutes.ChatScreen(
                                mode = _mode ,
                                ask = prompt,
                                urlBitmap = imgUrl
                            )
                        ){
                            popUpTo(navController.graph.startDestinationId)
                        }
                    }
                )
            }

            composable<ScreenRoutes.ResearchAndAnalysis> {
                ResearchAndAnalysisScreen(
                    onSend = {_mode, prompt,imgUrl ->
                        navController.navigate(
                            ScreenRoutes.ChatScreen(
                                mode = _mode ,
                                ask = prompt,
                                urlBitmap = imgUrl
                            )
                        ){
                            popUpTo(navController.graph.startDestinationId)
                        }
                    }
                )
            }

            composable<ScreenRoutes.WriteAnEssay> {
                WriteAnEssayScreen(
                    onSend = {_mode, prompt,imgUrl ->
                        navController.navigate(
                            ScreenRoutes.ChatScreen(
                                mode = _mode ,
                                ask = prompt,
                                urlBitmap = imgUrl
                            )
                        ){
                            popUpTo(navController.graph.startDestinationId)
                        }
                    }
                )
            }

            composable<ScreenRoutes.UserDeleteScreen> { navBackStackEntry ->
                val parameters = navBackStackEntry.toRoute<ScreenRoutes.UserDeleteScreen>()

                UserDeleteScreen(
                    parameters.userId,
                    parameters.username,
                    parameters.profilePictureUrl,
                    parameters.phoneNumber,
                    parameters.email,
                    coin = coinTotal,
                )
            }

            composable<ScreenRoutes.ChatScreen> { navBackStackEntry ->
                val parameters = navBackStackEntry.toRoute<ScreenRoutes.ChatScreen>()
                debugLog(" ${parameters.mode.id}")
                ChatBotCompose(
                    parameters.idHistory,
                    parameters.ask,
                    parameters.urlBitmap,
                    parameters.mode
                )
            }
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize()
    ) { innerPadding ->
        AppBox {
            renderNavHost(innerPadding)
//            if (
//                currentRouteName == ScreenRoutes.HomeScreen().route
//                || currentRouteName == ScreenRoutes.ChatScreen().route
//                || currentRouteName == ScreenRoutes.CoinHistory().route
//                || currentRouteName == ScreenRoutes.InAppPurchaseRoute().route
//                || currentRouteName == ScreenRoutes.ScanDetail().route
//            ) {
//                AppRowCentered(
//                    modifier = Modifier
//                        .padding(innerPadding)
//                        .minWidth(AppSize.MIN_TOUCH_SIZE)
//                        .height(AppSize.APPBAR_HEIGHT)
//                        .align(Alignment.TopEnd)
//                        .appClickable {
//                            if (!RconfAssist.getBoolean(RconfConst.IS_SHOW_IAP))
//                                return@appClickable
//                            if (currentRouteName == ScreenRoutes.InAppPurchaseRoute().route)
//                                return@appClickable
//                            appNavigator.navigateTo(ScreenRoutes.InAppPurchaseRoute())
//                        },
//                ) {
//                    // Số coin
//                    AppIcon(
//                        R.drawable.svg_ic_coin,
//                        iconSize = 14.dp,
//                        color = Color.Yellow,
//                    )
//                    AppSpacer(4.dp)
//                    AppText(
//                        fontSize = 12.5.sp,
//                        text =
//                        if (currentRouteName == ScreenRoutes.InAppPurchaseRoute().route) {
//                            String.format(Locale.getDefault(), "%,d", coinTotal)
//                        } else {
//                            if (coinTotal > 1000000)
//                                "${String.format(Locale.getDefault(), "%,d", (coinTotal.toDouble() / 1000000).roundToLong())}M"
//                            else if (coinTotal > 10000)
//                                "${String.format(Locale.getDefault(), "%,d", (coinTotal.toDouble() / 1000).roundToLong())}K"
//                            else String.format(Locale.getDefault(), "%,d", coinTotal)
//                        },
//                        color = Color.Yellow
//                    )
//
//                }
//            }
        }
    }

    if (viewModel.coinViewModel.isShowCoinInsufficientDialog.collectAsState().value) {
        @Composable
        fun ButtonRewardAd() {
            val isHighlight = true
            AppBox(
                modifier = Modifier
                    .conditional(isHighlight) {
                        padding(top = 10.dp)
                    }
            ) {
                AppButton(
                    modifier = Modifier.minSize(AppSize.MIN_TOUCH_SIZE),
                    onClick = {
                        if (!AdvertsManagerReward.checkRewardAdverts(AdvertsManagerReward.rewardAds)) {
                            MixedUtils.showToast(context, R.string.reward_ad_not_ready)
                            AdvertsManagerReward.requestRewardAdverts(arrayOf(AdvertsIdsMangerRewardAd.adIdRewardAd))
                            return@AppButton
                        }
                        AdvertsManagerReward.showRewardAd(
                            (context as CommActivity),
                            AdvertsManagerReward.rewardAds,
                            onUserEarnedReward = {
                                val coinNumber = RconfAssist.getInt(RconfConst.CREDIT_WATCH_REWARD_AD).toLong()
                                val coinString = String.format(Locale.getDefault(), "%,d", coinNumber)
                                viewModel.coinViewModel.updateCoinBalance(
                                    coinNumber,
                                    context.getString(R.string.txtid_watch_reward_ad)
                                )
                                MixedUtils.showToast(context, context.getString(R.string.txtid_coins) + ": +$coinString")
                            },
                            onShowAdCompleteListener = {
                                viewModel.coinViewModel.hideInsufficientCoinDialog()
                            }
                        )
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor =
                        if (isHighlight) AppColors.current.buttonActive
                        else AppColors.current.backgroundContentChatBox,
                        disabledContainerColor = AppColors.current.buttonInactive,
                        contentColor = AppColors.current.buttonText,
                        disabledContentColor = AppColors.current.buttonInactiveText
                    ),
                ) {
                    AppIcon(
                        R.drawable.svg_ic_reward_ad,
                        tint = Color.White
                    ){}
                    AppSpacer(8.dp)
                    AppColumn(contentAlignment = Alignment.CenterStart) {
                        AppText(
                            text = stringResource(R.string.txtid_watch_ad),
                            fontSize = 12.5.sp,
                            color = Color.White,
                            fontWeight = FontWeight.Medium,
                            maxLines = 1,
                        )
                        AppText(
                            text = stringResource(
                                R.string.get_number_coins,
                                String.format(Locale.getDefault(), "%,d", RconfAssist.getInt(RconfConst.CREDIT_WATCH_REWARD_AD))
                            ),
                            fontSize = 14.5.sp,
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            maxLines = 1,
                        )
                    }
                    AppSpacer(Modifier.weight(1f))
                }

                if (isHighlight) {
                    AppText(
                        text = stringResource(R.string.txtid_free),
                        fontSize = 11.sp,
                        fontWeight = FontWeight.Bold,
                        color = AppColors.current.text,
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .offset((-16).dp, (-12).dp)
                            .clipCorner(20.dp)
                            .background(AppColors.current.backgroundContentChatBox)
                            .padding(4.dp)
                    )
                }
            }
        }


        Dialog(
            onDismissRequest = {
                viewModel.coinViewModel.hideInsufficientCoinDialog()
            }
        ) {
            AppBox(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        color = MaterialTheme.colorScheme.background,
                        shape = RoundedCornerShape(16.dp)
                    )
            ) {
                AppColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    AppRowCentered(modifier = Modifier.padding(bottom = 16.dp)) {
                        AppText(
                            text = context.getString(R.string.txtid_insufficient_credits),
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Medium
                        )
                        AppSpacer(6.dp)
                        AppBoxCentered {
                            AppIcon(
                                R.drawable.svg_ic_coin_stack,
                                size = 15.5.dp,
                                tint = AppColors.current.primary,
                            )
                            AppIcon(
                                R.drawable.svg_ic_coin_stack,
                                size = 14.dp,
                                tint = Color.Yellow,
                            )
                        }
                    }


                    AppText(
                        text = context.getString(R.string.txtid_not_enough_coin),
                        fontSize = 14.sp,
                        modifier = Modifier.padding(bottom = 24.dp)
                    )

                    ButtonRewardAd()
                    AppSpacer(12.dp)

                    AppButton(
                        modifier = Modifier.minSize(AppSize.MIN_TOUCH_SIZE),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = AppColors.current.backgroundContentChatBox,
                            disabledContainerColor = AppColors.current.buttonInactive,
                            contentColor = AppColors.current.buttonText,
                            disabledContentColor = AppColors.current.buttonInactiveText
                        ),
                        onClick = {
                            viewModel.coinViewModel.hideInsufficientCoinDialog()
                            appNavigator.navigateTo(ScreenRoutes.InAppPurchaseRoute())
                        },
                        colorShadow = null,
                    ) {
                        AppRowCentered {
                            AppIcon(
                                R.drawable.ic_try_premium_btn,
                                size = 20.dp,
                                tint = null,
                            )
                            AppSpacer(8.dp)
                            AppText(
                                text = stringResource(R.string.get_coins),
                                color = AppColors.current.text,
                                fontSize = 16.sp,
                                lineHeight = 18.sp,
                                fontWeight = FontWeight.W800
                            )
                            AppSpacer(Modifier.weight(1f))
                        }
                    }
                    AppSpacer(8.dp)

                    AppButtonText(
                        modifier = Modifier.fillMaxWidth(),
                        text = context.getString(R.string.txtid_cancel),
                        textColor = AppColors.current.text,
                        colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent),
                        colorShadow = null,
                    ) {
                        viewModel.coinViewModel.hideInsufficientCoinDialog()
                    }
                }
            }
        }
    }

}
