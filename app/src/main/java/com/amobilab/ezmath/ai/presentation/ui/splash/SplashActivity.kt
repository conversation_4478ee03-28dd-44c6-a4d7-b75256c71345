package com.amobilab.ezmath.ai.presentation.ui.splash

import amobi.module.common.CommApplication
import amobi.module.common.advertisements.reward_ad.AdvertsManagerReward
import amobi.module.common.configs.CommFigs
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.DebugLogCustom
import amobi.module.common.utils.FirebaseAssist
import amobi.module.compose.foundation.AppBox
import amobi.module.gpdr.GDPRAssist
import amobi.module.gpdr.GDPRConsent
import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.lifecycleScope
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.admob.AdvertsIdsMangerRewardAd
import com.amobilab.ezmath.ai.app.BaseActivity
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_components.VideoBackground
import com.amobilab.ezmath.ai.presentation.navigation.MainEntryActivity
import com.amobilab.ezmath.ai.presentation.ui.onboarding.OnboardingActivity
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.FirebaseAnalytics.ConsentType
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.EnumMap
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.max
import kotlin.math.round

@SuppressLint("CustomSplashScreen")
@AndroidEntryPoint
class SplashActivity : BaseActivity() {
    @Composable
    override fun MainContentCompose() {
        SplashCompose()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initGDPR()
    }

    private fun initGDPR() {

        GDPRConsent(this) {
            val gpdrIsEnabled = GDPRAssist.isGDPR()
            val gpdrCanShowAds = if (gpdrIsEnabled) GDPRAssist.canShowAds() else true
            val gpdrCanShowPersonalizedAds = if (gpdrIsEnabled) GDPRAssist.canShowPersonalizedAds() else true
            if (CommFigs.IS_DEBUG) {
                DebugLogCustom.logd("GDPRConsent enabled:  $gpdrIsEnabled")
                DebugLogCustom.logd("GDPRConsent showAds:  $gpdrCanShowAds")
                DebugLogCustom.logd("GDPRConsent showPersonalizedAds:  $gpdrCanShowPersonalizedAds")
            }

            Firebase.analytics.setConsent(
                EnumMap<ConsentType, FirebaseAnalytics.ConsentStatus>(ConsentType::class.java).apply {
                    put(ConsentType.ANALYTICS_STORAGE, FirebaseAnalytics.ConsentStatus.GRANTED)
                    put(ConsentType.AD_STORAGE,
                        if (gpdrCanShowAds)
                            FirebaseAnalytics.ConsentStatus.GRANTED
                        else FirebaseAnalytics.ConsentStatus.DENIED
                    )
                    put(ConsentType.AD_USER_DATA,
                        if (gpdrCanShowAds)
                            FirebaseAnalytics.ConsentStatus.GRANTED
                        else FirebaseAnalytics.ConsentStatus.DENIED
                    )
                    put(
                        ConsentType.AD_PERSONALIZATION,
                        if (gpdrCanShowPersonalizedAds)
                            FirebaseAnalytics.ConsentStatus.GRANTED
                        else FirebaseAnalytics.ConsentStatus.DENIED
                    )
                }
            )

            onGDPRCheckComplete()

            return@GDPRConsent Unit
        }
    }

    private fun onGDPRCheckComplete() {
        val currentMillis = System.currentTimeMillis()
        CoroutineScope(Dispatchers.IO).launch {
            // Initialize the Google Mobile Ads SDK on a background thread.
            CommApplication.initAdMob(this@SplashActivity) {
                val durationInSeconds = round((System.currentTimeMillis() - currentMillis) / 1000.0)
                FirebaseAssist.instance.logCustomEvent(
                    "admob_init",
                    Bundle().apply {
                        putString("duration", durationInSeconds.toString())
                    }
                )
            }
            runOnUiThread {
                AdvertsManagerReward.requestRewardAdverts(arrayOf(AdvertsIdsMangerRewardAd.adIdRewardAd))
            }
        }


        val capWaitMillis = max(RconfAssist.getInt(RconfConst.SPLASH_TIME_OUT), 2) * CommFigs.MILLIS_SECOND

        this.lifecycleScope.launchWhenStarted {
            delay(capWaitMillis + 200)
            if (isShowAdSuccess.get())
                return@launchWhenStarted
            runNextActivity()
        }
    }


    private val isRanNextActivityCalled = AtomicBoolean(false)
    private val isShowAdSuccess = AtomicBoolean(false)
    private fun runNextActivity(i: Intent? = null) {
        if (isRanNextActivityCalled.getAndSet(true)) {
            return
        }

        // Kiểm tra xem người dùng đã xem onboarding chưa
        val isOnboardingCompleted = PrefAssist.getBoolean(PrefConst.ONBOARDING_COMPLETED, false)

        val targetActivity = if (isOnboardingCompleted) {
            MainEntryActivity::class.java
        } else {
            OnboardingActivity::class.java
        }

        val intent = Intent(this, targetActivity)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            .addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)

        startActivity(intent)
    }

}

@Composable
private fun SplashCompose() {
    var progress by remember { mutableFloatStateOf(0f) }
    val context = LocalContext.current

    Scaffold { _ ->
        // Box chứa toàn bộ nội dung để có thể đặt video làm background
        AppBox(
            modifier = Modifier
                .fillMaxSize()
        ) {
            // Video background với hình ảnh background khi video chưa load xong
            VideoBackground(
                videoResId = R.raw.background_splash,
                modifier = Modifier.fillMaxSize(),
                backgroundImageResId = R.drawable.bg_on_2 // Sử dụng hình ảnh làm background
            )


            // Lớp overlay để làm tối video (tùy chọn)
//            Box(
//                modifier = Modifier
//                    .fillMaxSize()
//                    .background(Color.Black.copy(alpha = 0.4f))
//            )

            // Nội dung chính
//            AppColumnCentered(
//                modifier = Modifier.fillMaxSize()
//            ) {
//                AppSpacer(32.dp)
//
//                AppSpacer(Modifier.weight(1f))
//
//                AppGlideImage(
//                    modifier = Modifier.size(100.dp),
//                    resId = R.drawable.ic_launcher
//                )
//
//                AppSpacer(16.dp)
//
//                // Tên ứng dụng
//                AppText(
//                    text = stringResource(R.string.app_name),
//                    fontSize = 28.sp,
//                    fontWeight = FontWeight.Bold,
//                    color = Color.White // Đổi màu chữ thành trắng để dễ đọc trên nền video
//                )
//
//                AppSpacer(8.dp)
//
//                // Slogan
//                AppText(
//                    text = stringResource(R.string.learn_math_the_easy_way),
//                    fontSize = 16.sp,
//                    color = Color.White.copy(alpha = 0.8f) // Màu trắng với độ trong suốt
//                )
//
//                AppSpacer(32.dp)
//
//                // Progress bar ngang
//                LinearProgressIndicator(
//                    modifier = Modifier
//                        .fillMaxWidth(0.8f)
//                        .height(6.dp),
//                    color = Color.White, // Đổi màu thành trắng
//                )
//
//                AppSpacer(Modifier.weight(1f))
//
//                AndroidView(
//                    modifier = Modifier
//                        .padding(horizontal = 16.dp),
//                    factory = { context ->
//                        val hyperLink = "<a href='https://amobilab.com/policy-ez-math.html'>${
//                            context.getString(R.string.terms_and_services)
//                        }</a>"
//                        var html = "<p>" + context.getString(
//                            R.string.accept_terms_here,
//                            hyperLink
//                        ) + "</p>"
//                        // if hyper not working, add it to the end of the string
//                        if (!html.contains(hyperLink)) {
//                            html += "<p><a href='https://amobilab.com/policy-ez-math.html'>${
//                                context.getString(
//                                    R.string.terms_and_services
//                                )
//                            }</a></p>"
//                        }
//                        TextView(context).apply {
//                            text = Html.fromHtml(html)
//                            typeface = ResourcesCompat.getFont(
//                                context, amobi.module.compose.theme.R.font.roboto_medium
//                            )
//                            setTextColor(Color.White.toArgb()) // Đổi màu chữ thành trắng
//
//                            movementMethod = LinkMovementMethod.getInstance()
//                        }
//                    }
//                )
//            }
        }
    }
}