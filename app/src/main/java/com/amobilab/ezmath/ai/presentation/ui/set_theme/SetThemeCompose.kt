package com.amobilab.ezmath.ai.presentation.ui.set_theme

import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppColumnCentered
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.shared_components.AppAppbar
import com.amobilab.ezmath.ai.presentation.common.shared_values.AppThemeMode

@Composable
fun SetThemeCompose(onClick: () -> Unit) {
    val viewModel = hiltViewModel<SetThemeViewModel>()
    val currentAppTheme by viewModel.coinViewModel.appThemeMode.observeAsState()
    // Convert string theme to enum
    val themeMode = when (currentAppTheme) {
        AppThemeMode.DARK -> AppThemeMode.DARK
        AppThemeMode.LIGHT -> AppThemeMode.LIGHT
        else -> AppThemeMode.SYSTEM
    }

    Scaffold { innerPadding ->
        AppColumn(
            modifier = Modifier
                .fillMaxSize()
        ) {
            AppAppbar(
                modifier = Modifier.padding(innerPadding),
                innerPadding = PaddingValues(0.dp),
                title = stringResource(R.string.txtid_theme),
                onBack = onClick
            )
            AppSpacer(16.dp)
            AppColumn(modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)) {
                AppText(
                    modifier = Modifier.padding(start = 12.dp),
                    text = stringResource(R.string.select_your_theme).uppercase(),
                    fontSize = AppFontSize.BODY2,
                    fontWeight = FontWeight.W400,
                    lineHeight = 20.sp,
                    color = AppColors.current.titleContent
                )
                AppSpacer(4.dp)

                // Display preview image based on selected theme
                Image(
                    modifier = Modifier.fillMaxWidth(),
                    painter = painterResource(
                        id = when (themeMode) {
                            AppThemeMode.LIGHT -> R.drawable.preview_theme_light
                            AppThemeMode.DARK -> R.drawable.preview_theme_dark
                            AppThemeMode.SYSTEM -> if (currentAppTheme == AppThemeMode.DARK)
                                R.drawable.preview_theme_dark else R.drawable.preview_theme_light
                        }
                    ),
                    contentDescription = null
                )

                AppSpacer(12.dp)
                AppRow(modifier = Modifier.fillMaxWidth()) {
                    // System theme button
                    ThemeButton(
                        modifier = Modifier.weight(1f),
                        title = stringResource(R.string.txtid_system),
                        iconRes = R.drawable.ic_theme_system,
                        isSelected = themeMode == AppThemeMode.SYSTEM,
                        onClick = { viewModel.coinViewModel.setAppTheme(AppThemeMode.SYSTEM) }
                    )

                    AppSpacer(8.dp)

                    // Dark theme button
                    ThemeButton(
                        modifier = Modifier.weight(1f),
                        title = stringResource(R.string.txtid_dark),
                        iconRes = R.drawable.ic_theme_dark,
                        isSelected = themeMode == AppThemeMode.DARK,
                        onClick = { viewModel.coinViewModel.setAppTheme(AppThemeMode.DARK) }
                    )

                    AppSpacer(8.dp)

                    // Light theme button
                    ThemeButton(
                        modifier = Modifier.weight(1f),
                        title = stringResource(R.string.txtid_light),
                        iconRes = R.drawable.ic_theme_light,
                        isSelected = themeMode == AppThemeMode.LIGHT,
                        onClick = { viewModel.coinViewModel.setAppTheme(AppThemeMode.LIGHT) }
                    )
                }
            }
        }
    }
}

@Composable
private fun ThemeButton(
    modifier: Modifier = Modifier,
    title: String,
    iconRes: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = if (isSelected)
        AppColors.current.selectThemeButtonBackground else AppColors.current.unSelectThemeButtonBackground  //color

    val buttonModifier = if (isSelected) {
        modifier
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor, shape = RoundedCornerShape(8.dp))
            .clickable { onClick() }
            .padding(vertical = 12.dp, horizontal = 8.dp)
    } else {
        modifier
            .border(width = 1.dp, color = AppColors.current.borderColorThemeButton, shape = RoundedCornerShape(8.dp)) //color
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor, shape = RoundedCornerShape(8.dp))
            .clickable { onClick() }
            .padding(vertical = 12.dp, horizontal = 8.dp)
    }

    AppColumnCentered(
        modifier = buttonModifier
    ) {
        Image(
            modifier = Modifier
                .size(24.dp)
                .padding(bottom = 4.dp),
            painter = painterResource(id = iconRes),
            contentDescription = null,
            colorFilter = if (isSelected) ColorFilter.tint(color = AppColors.current.selectThemeButtonText) else ColorFilter.tint(color =AppColors.current.unSelectThemeButtonText) //color
        )
        AppSpacer(4.dp)
        AppText(
            text = title,
            fontSize = AppFontSize.BODY1,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
            lineHeight = 24.sp,
            color = if (isSelected) AppColors.current.selectThemeButtonText else AppColors.current.unSelectThemeButtonText
        )
    }
}