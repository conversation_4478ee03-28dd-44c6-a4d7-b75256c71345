package com.amobilab.ezmath.ai.presentation.ui.authentication

import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppGlideImage
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppThemeWrapper
import android.app.Activity.RESULT_OK
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Icon
import androidx.compose.material.OutlinedButton
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.TextFieldDefaults
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.presentation.common.shared_components.AppAppbar
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainViewModel
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.NavigatorViewModel
import com.amobilab.ezmath.ai.presentation.navigation.MainEntryActivity
import com.amobilab.ezmath.ai.utils.GoogleAuthUiClient
import com.google.android.gms.auth.api.identity.Identity
import kotlinx.coroutines.launch

@AppPreview
@Composable
fun AuthScreenPreview() {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        AuthScreen()
    }
}

@Composable
fun AuthScreen() {
    val navigatorViewModel: NavigatorViewModel? = if (PreviewAssist.IS_PREVIEW)
        null
    else
        hiltViewModel(LocalContext.current as MainEntryActivity)

    val email = remember { mutableStateOf("") }
    val password = remember { mutableStateOf("") }
    val confirmPassword = remember { mutableStateOf("") }
    val isLoading = remember { mutableStateOf(false) }
    val viewModel: AuthViewModel = hiltViewModel()
    val viewModelHome: MainViewModel = hiltViewModel()
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    val localFocusManager = LocalFocusManager.current

    // Theo dõi xem người dùng đang ở chế độ Đăng nhập hay Đăng ký
    val isSignUpMode = remember { mutableStateOf(false) }

    val messageText = remember { mutableStateOf("") }

    val emailRegex = Regex("^[\\w-.]+@[\\w-]+\\.[a-zA-Z]{2,4}$")
    val passwordRegex2 = Regex("^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{8,}\$") // Ít nhất 8 ký tự, gồm chữ và số
    val passwordRegex = Regex("^.{8,}$") // Ít nhất 8 ký tự ,

    val emailError = remember { mutableStateOf(false) }
    val passwordError = remember { mutableStateOf(false) }


    val googleAuthUiClient by lazy {
        GoogleAuthUiClient(
            oneTapClient = Identity.getSignInClient(context),
            viewModel = viewModelHome
        )
    }

    val state by viewModel.state.collectAsStateWithLifecycle()

    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult(),
        onResult = { result ->
            if (result.resultCode == RESULT_OK) {
                coroutineScope.launch {
                    val signInResult = googleAuthUiClient.signInWithIntent(
                        intent = result.data ?: return@launch
                    )
                    viewModel.onSignInResult(signInResult)
                }
            }
        }
    )

    LaunchedEffect(key1 = state.isSignInSuccessful) {
        if (state.isSignInSuccessful) {
            Toast.makeText(context, context.getString(R.string.sign_in_successful), Toast.LENGTH_LONG).show()
            navigatorViewModel?.navigateBack()
            viewModel.resetState()
        }
    }

    Scaffold { innerPadding ->
        AppColumn(Modifier.fillMaxSize()) {
            AppAppbar(
                innerPadding = innerPadding,
                title = if (isSignUpMode.value) stringResource(R.string.sign_up)
                else stringResource(R.string.txtid_login),
                onBack = { navigatorViewModel?.navigateBack() },
            )
            AppColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.background)
                    .padding(innerPadding)
                    .padding(20.dp)
                    .pointerInput(Unit) {
                        detectTapGestures(onTap = {
                            localFocusManager.clearFocus()
                        })
                    },
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center

            ) {
                AppSpacer(15.dp)
                AppGlideImage(
                    modifier = Modifier.size(100.dp),
                    resId = R.drawable.ic_launcher
                )
                AppSpacer(50.dp)

                if (messageText.value.isNotEmpty()) {
                    Text(
                        text = messageText.value,
                        color = MaterialTheme.colorScheme.error, // Hoặc màu phù hợp
                        style = TextStyle(fontSize = 14.sp),
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                }

                OutlinedTextField(
                    value = email.value,
                    onValueChange = {
                        email.value = it
                        emailError.value = !emailRegex.matches(it)
                    },
                    leadingIcon = {
                        Icon(
                            Icons.Default.Person,
                            contentDescription = stringResource(R.string.txtid_person),
                            tint = AppColors.current.text,
                        )
                    },
                    isError = emailError.value,
                    label = { Text(text = stringResource(R.string.txtid_email)) },
                    placeholder = { Text(text = stringResource(R.string.enter_email)) },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(10.dp),
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        textColor = AppColors.current.text,
                        focusedBorderColor = AppColors.current.text,
                        unfocusedBorderColor = AppColors.current.textHintColor,
                        focusedLabelColor = AppColors.current.text,
                        unfocusedLabelColor = AppColors.current.textHintColor,
                        placeholderColor = AppColors.current.text
                    )
                )

                AppSpacer(5.dp)

                OutlinedTextField(
                    value = password.value,
                    onValueChange = {
                        password.value = it
                        passwordError.value = !passwordRegex.matches(it)
                    },
                    leadingIcon = {
                        Icon(
                            painterResource(R.drawable.ic_password),
                            contentDescription = stringResource(R.string.password),
                            tint = AppColors.current.text
                        )
                    },
                    isError = passwordError.value,
                    label = { Text(text = stringResource(R.string.password)) },
                    placeholder = { Text(text = stringResource(R.string.enter_password)) },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(10.dp),
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        textColor = AppColors.current.text,
                        focusedBorderColor = AppColors.current.text,
                        unfocusedBorderColor = AppColors.current.textHintColor,
                        focusedLabelColor = AppColors.current.text,
                        unfocusedLabelColor = AppColors.current.textHintColor,
                        placeholderColor = AppColors.current.text
                    )
                )

                // Nếu đang ở chế độ Đăng ký, hiển thị trường "Xác nhận mật khẩu"
                if (isSignUpMode.value) {
                    AppSpacer(5.dp)
                    OutlinedTextField(
                        value = confirmPassword.value,
                        onValueChange = {
                            confirmPassword.value = it
                        },
                        leadingIcon = {
                            Icon(
                                painterResource(R.drawable.ic_password),
                                contentDescription = stringResource(R.string.confirm_password),
                                tint = AppColors.current.text,
                            )
                        },
                        isError = confirmPassword.value != password.value,
                        label = { Text(text = stringResource(R.string.confirm_password)) },
                        placeholder = { Text(text = stringResource(R.string.re_enter_password)) },
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(10.dp),
                        colors = TextFieldDefaults.outlinedTextFieldColors(
                            textColor = AppColors.current.text,
                            focusedBorderColor = AppColors.current.text,
                            unfocusedBorderColor = AppColors.current.textHintColor,
                            focusedLabelColor = AppColors.current.text,
                            unfocusedLabelColor = AppColors.current.textHintColor,
                            placeholderColor = AppColors.current.text
                        )
                    )
                }

                AppSpacer(25.dp)

                Button(
                    onClick = {
                        coroutineScope.launch {
                            isLoading.value = true // Start loading
                            when {
                                email.value.isEmpty() -> {
                                    debugLog("aaaa: Email cannot be empty")
                                    isLoading.value = false
                                    messageText.value = context.getString(R.string.email_cannot_be_empty)
                                }

                                password.value.isEmpty() -> {
                                    debugLog("aaaa: password")
                                    isLoading.value = false
                                    messageText.value = context.getString(R.string.password_cannot_be_empty)
                                }

                                isSignUpMode.value && confirmPassword.value != password.value -> {
                                    isLoading.value = false
                                    messageText.value = context.getString(R.string.passwords_do_not_match)
                                }

                                else -> {
                                    messageText.value = ""
                                    val signInResult = if (isSignUpMode.value) {
                                        googleAuthUiClient.signUpWithEmail(email.value.trim().lowercase(), password.value, email.value.trim().lowercase())
                                    } else {
                                        googleAuthUiClient.signInWithEmail(email.value.trim().lowercase(), password.value)
                                    }
                                    isLoading.value = false // Stop loading
                                    if (signInResult.data != null) {

                                        val message = context.getString(R.string.txtid_successful) +
                                                " " +
                                                (if (isSignUpMode.value) context.getString(R.string.sign_up) else context.getString(R.string.sign_in)) +
                                                " " +
                                                signInResult.data.username
                                        messageText.value = message
                                        MixedUtils.showToast(context, message)
                                    } else {
                                        val message = context.getString(R.string.txtid_failed) + " " +
                                                (if (isSignUpMode.value) context.getString(R.string.sign_up) else context.getString(R.string.sign_in)) + " " +
                                                signInResult.errorMessage
                                        messageText.value = message
                                    }
                                }
                            }
                        }
                    },
                    colors = ButtonDefaults.buttonColors(
                        backgroundColor = MaterialTheme.colorScheme.primary, // Màu nền của button
                        contentColor = Color.White // Màu văn bản của button
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 10.dp)
                        .height(50.dp),
                ) {
                    if (isLoading.value) {
                        CircularProgressIndicator(color = MaterialTheme.colorScheme.onPrimary)
                    } else {
                        AppText(
                            text = if (isSignUpMode.value) context.getString(R.string.sign_up)
                            else context.getString(R.string.txtid_login),
                            fontSize = 15.sp
                        )
                    }
                }

                AppSpacer(25.dp)

                AppRow(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = if (isSignUpMode.value) stringResource(R.string.already_have_an_account) else stringResource(R.string.don_t_have_an_account),
                        style = TextStyle(fontSize = 15.sp),
                        color = AppColors.current.text
                    )
                    AppSpacer(modifier = Modifier.width(3.dp))
                    TextButton(
                        onClick = {
                            isSignUpMode.value = !isSignUpMode.value
                            confirmPassword.value = "" // Clear confirm password field when switching modes
                        }
                    ) {
                        Text(
                            text = if (isSignUpMode.value) context.getString(R.string.txtid_login) else context.getString(R.string.sign_up),
                            color = MaterialTheme.colorScheme.primary,
                        )
                    }
                }

                AppSpacer(8.dp)

                MyOutlinedButton(stringResource(R.string.login_with_google), R.drawable.ic_google) {
                    coroutineScope.launch {
                        val signInIntentSender = googleAuthUiClient.signIn()
                        launcher.launch(
                            IntentSenderRequest.Builder(signInIntentSender ?: return@launch).build()
                        )
                    }
                }
            }

        }
    }
}

@Composable
fun MyOutlinedButton(
    label: String,
    img: Int,
    onClick: () -> Unit,
) {
    OutlinedButton(
        onClick = onClick, modifier = Modifier
            .height(40.dp)
            .width(350.dp)
    ) {
        Image(
            painter = painterResource(img),
            contentDescription = "",
        )
        AppSpacer(modifier = Modifier.size(8.dp))
        Text(
            label,
            fontSize = 15.sp,
            color = MaterialTheme.colorScheme.primary
        )
    }
}
