package com.amobilab.ezmath.ai.presentation.ui.onboarding

import android.content.Intent
import android.os.Bundle
import androidx.compose.runtime.Composable
import com.amobilab.ezmath.ai.app.BaseActivity
import com.amobilab.ezmath.ai.presentation.navigation.MainEntryActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OnboardingActivity : BaseActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }
    
    @Composable
    override fun MainContentCompose() {
        OnboardingCompose(
            onFinish = {
                navigateToMainActivity()
            }
        )
    }
    
    private fun navigateToMainActivity() {
        val intent = Intent(this, MainEntryActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            .addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
        startActivity(intent)
        finish()
    }
}
