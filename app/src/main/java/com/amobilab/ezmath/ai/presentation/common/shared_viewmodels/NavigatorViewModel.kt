package com.amobilab.ezmath.ai.presentation.common.shared_viewmodels

import androidx.lifecycle.ViewModel
import com.amobilab.ezmath.ai.presentation.navigation.AppNavigator
import com.amobilab.ezmath.ai.presentation.navigation.ScreenRoutes
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class NavigatorViewModel @Inject constructor(
    private val appNavigator: AppNavigator
) : ViewModel() {
    fun navigateTo(appNavScreen: ScreenRoutes) {
        appNavigator.navigateTo(appNavScreen)
    }

    fun navigateBack() {
        appNavigator.navigateBack()
    }
}