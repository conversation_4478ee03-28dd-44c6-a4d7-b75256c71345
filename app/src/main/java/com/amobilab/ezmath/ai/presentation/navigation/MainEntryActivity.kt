package com.amobilab.ezmath.ai.presentation.navigation

import android.os.Bundle
import androidx.compose.runtime.Composable
import com.amobilab.ezmath.ai.app.BaseActivity
import com.amobilab.ezmath.ai.configs.AppSecret
import com.amobilab.ezmath.ai.utils.IapAssist
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MainEntryActivity : BaseActivity() {
    companion object {
        private const val TAG = "HomeActivity"
    }

    @Inject
    lateinit var appNavigator: AppNavigator

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize the secret first time
        AppSecret.initializeSecret(this)

        IapAssist.instance.connect()
    }

    override fun onDestroy() {
        super.onDestroy()

        IapAssist.instance.disconnect()
    }

    @Composable
    override fun MainContentCompose() {
        MainEntryCompose(appNavigator = appNavigator)
    }
}