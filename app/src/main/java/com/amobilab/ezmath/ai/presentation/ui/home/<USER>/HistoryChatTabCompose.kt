package com.amobilab.ezmath.ai.presentation.ui.home.tabs

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.capitaliseEachWord
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.extentions.minWidth
import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppBoxCentered
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppColumnCentered
import amobi.module.compose.foundation.AppDivider
import amobi.module.compose.foundation.AppGlideImage
import amobi.module.compose.foundation.AppIcon
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontFamily
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppSize
import amobi.module.compose.theme.AppThemeWrapper
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.SwipeableState
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.db.entities.HistoryEntity
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.share_dialog.CommonDialog
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainViewModel
import com.amobilab.ezmath.ai.utils.StringUtils
import com.kevinnzou.swipebox.SwipeBox
import com.kevinnzou.swipebox.SwipeDirection
import com.kevinnzou.swipebox.widget.SwipeIcon
import kotlinx.coroutines.launch
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Locale

@AppPreview
@Composable
fun HistoryChatTabComposePreview() {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        HistoryChatTabCompose(
            innerPaddingHome = PaddingValues(),
            onClick = {},
            onOffBottomBar = {}
        )
    }
}

// Hàm chuyển đổi timestamp (Long) thành LocalDate
fun Long.toLocalDate(): LocalDate {
    return Instant.ofEpochMilli(this) // Chuyển từ timestamp
        .atZone(ZoneId.systemDefault()) // Lấy timezone hệ thống
        .toLocalDate() // Chuyển sang LocalDate
}

enum class SortOption(val displayName: String) {
    DATE("Date"),
    NAME("Name"),
    FAVORITES("Favorites")
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun HistoryChatTabCompose(innerPaddingHome: PaddingValues, onClick: (String) -> Unit , onOffBottomBar: (Boolean) -> Unit = {}) {
    val viewModel = hiltViewModel<MainViewModel>()

    // Theo dõi danh sách lịch sử và trạng thái loading
    val historyList by viewModel.historyList.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()

    //====================================================================================================

    var selectedSortOption by remember {
        mutableStateOf(
            PrefAssist.getString(
                PrefConst.SORT_BY,
                SortOption.DATE.name
            )
        )
    }
    val textState = remember { mutableStateOf(TextFieldValue("")) }
    var showMultiSelect by remember { mutableStateOf(false) }
    val selectedItems = remember { mutableStateListOf<Long>() }
//            var allSelected by remember { mutableStateOf(false) } // Trạng thái để kiểm tra xem đã chọn hết chưa
    val allSelected by remember { derivedStateOf { historyList.size == selectedItems.size } }

    val focusRequester = remember { FocusRequester() }

    val context = LocalContext.current

    LaunchedEffect(showMultiSelect) {
        onOffBottomBar(!showMultiSelect)
    }
    //====================================================================================================

    // Gọi loadHistory chỉ khi Composable được tạo
    LaunchedEffect(Unit) {
        viewModel.loadHistory()
    }

    // Hiển thị giao diện
    Scaffold { innerPadding ->
        AppColumn(Modifier.fillMaxSize()) {
//            AppAppbar(
//                innerPadding = innerPadding,
//                title = stringResource(R.string.txtid_history),
//            )
            //appbar
            AppRow(
                modifier = Modifier
                    .padding(top = innerPadding.calculateTopPadding())
                    .padding(vertical = 12.dp)
                    .padding(horizontal = 12.dp)
                ,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                if (
                    !showMultiSelect
                ){
                    AppText(
                        text = stringResource(R.string.chat_history),
                        modifier = Modifier,
                        fontSize = AppFontSize.TITLE2,
                        fontWeight = FontWeight.W700,
                        lineHeight = 28.sp,
                        color = AppColors.current.titleText,
                    )
                    AppSpacer(modifier = Modifier.weight(1f))
                }else{
                    AppRow(
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        AppIcon(
                            icon = R.drawable.abc_ic_clear_material,
                            size = AppSize.ICON_SIZE,
                            tint = AppColors.current.titleText,
                        ) {
                            showMultiSelect = false
                            selectedItems.clear()
                        }
                        AppText(
                            text = selectedItems.size.toString(),
                            fontSize = AppFontSize.TITLE2,
                            fontWeight = FontWeight.W700,
                            lineHeight = 28.sp,
                            color = AppColors.current.titleText,
                        )
                    }
                    AppSpacer(modifier = Modifier.weight(1f))
                    AppText(
                        modifier = Modifier.clickable {
                            // Chọn tất cả hoặc bỏ chọn tất cả các item
                            if (!allSelected) {
                                selectedItems.clear()
                                selectedItems.addAll(historyList.map { it.historyId.toLong() })
                            } else {
                                selectedItems.clear()
                            }
                        },
                        text = if (!allSelected) {
                            stringResource(R.string.select_all)
                        }else{
                            stringResource(R.string.unselect_all)
                        },
                        fontSize = AppFontSize.BODY1,
                        fontWeight = FontWeight.W500,
                        lineHeight = 24.sp,
                        color = Color(0xFF4BA1FF), //Color
                    )
                }
            }
            if (isLoading) {
                // Hiển thị loading indicator khi đang tải
                AppBoxCentered(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(bottom = innerPaddingHome.calculateBottomPadding())
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.width(48.dp),
                        color = MaterialTheme.colorScheme.secondary,
                        trackColor = MaterialTheme.colorScheme.surfaceVariant,
                    )
                }
            } else if (historyList.isNotEmpty()) {
                AppColumn(
                    Modifier
                        .fillMaxSize()
                        .padding(bottom = innerPaddingHome.calculateBottomPadding())
                ) {
                    if (!showMultiSelect) {
                        AppRow(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 14.dp)
                                .padding(horizontal = 12.dp)
                                .border(
                                    width = 1.dp,
                                    color = AppColors.current.borderSearch,
                                    shape = RoundedCornerShape(8.dp)
                                ),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            AppRow(
                                modifier = Modifier
                                    .weight(1f),
                                verticalAlignment = Alignment.CenterVertically

                            ) {
                                AppIcon(
                                    icon = R.drawable.ic_search_new,
                                    size = 16.dp,
                                    tint = AppColors.current.textHintColor,
                                ) { focusRequester.requestFocus() }

                                val interactionSource = remember { MutableInteractionSource() }
                                BasicTextField(
                                    modifier = Modifier
                                        .weight(1f)
                                        .focusRequester(focusRequester),
                                    value = textState.value,
                                    onValueChange = { value ->
                                        textState.value = value
                                    },
                                    visualTransformation = VisualTransformation.None,
                                    interactionSource = interactionSource,
                                    textStyle = TextStyle(
                                        color = AppColors.current.text,
                                        fontSize = AppFontSize.BODY1,
                                        fontFamily = AppFontFamily.get(),
                                        fontWeight = FontWeight.Medium,
                                    ),
                                    cursorBrush = SolidColor(AppColors.current.text),
                                    enabled = true,
                                    singleLine = true,
                                ) { innerTextField ->
                                    TextFieldDefaults.DecorationBox(
                                        value = textState.value.text,
                                        visualTransformation = VisualTransformation.None,
                                        innerTextField = innerTextField,
                                        singleLine = true,
                                        enabled = true,
                                        interactionSource = interactionSource,
                                        contentPadding = PaddingValues(vertical = 12.dp),
                                        shape = RoundedCornerShape(12.dp),
                                        colors = TextFieldDefaults.colors(
                                            focusedIndicatorColor = Color.Transparent,
                                            unfocusedIndicatorColor = Color.Transparent,
                                            disabledIndicatorColor = Color.Transparent,
                                            focusedContainerColor = Color.Transparent,
                                            unfocusedContainerColor = Color.Transparent,
                                        ),
                                        placeholder = {
                                            AppText(
                                                text = stringResource(R.string.search),
                                                color = AppColors.current.textHintColor,
                                                fontSize = AppFontSize.BODY2,
                                            )
                                        }
                                    )
                                }
                            }
                            // DropdownMenu for sorting
                            var expanded by remember { mutableStateOf(false) }
                            AppBox {
                                AppSpacer(
                                    modifier = Modifier
                                        .padding(6.dp)
                                        .size(AppSize.MIN_TOUCH_SIZE - 12.dp)
                                        .clip(RoundedCornerShape(10.dp))
                                )
                                AppIcon(
                                    R.drawable.ic_search_filter,
                                    size = 20.dp,
                                    tint = AppColors.current.textHintColor,
                                ) { expanded = true }

                                DropdownMenu(
                                    expanded = expanded,
                                    onDismissRequest = { expanded = false },
                                    shape = RoundedCornerShape(8.dp),
                                    modifier = Modifier
                                        .background(AppColors.current.backgroundDropdownMenu)
                                ) {
                                    DropdownMenuItem(
                                        onClick = {
                                            selectedSortOption = SortOption.DATE.name
                                            viewModel.saveSortBy(SortOption.DATE.name)
                                            expanded = false
                                        },
                                        text = {
                                            AppRow(
                                                modifier = Modifier.fillMaxWidth(),
                                                horizontalArrangement = Arrangement.SpaceBetween,
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                AppText(
                                                    text = stringResource(R.string.txtid_date),
                                                    fontSize = AppFontSize.BODY1,
                                                    fontWeight = FontWeight.W400,
                                                    lineHeight = 20.sp,
                                                    color = AppColors.current.titleText
                                                )
                                                AppSpacer(8.dp)

                                                if (selectedSortOption == SortOption.DATE.name) {
                                                    AppIcon(
                                                        R.drawable.svg_comm_ic_check,
                                                        size = 24.dp,
                                                        tint = AppColors.current.titleText,
                                                    )
                                                }
                                            }
                                        }
                                    )
                                    DropdownMenuItem(
                                        onClick = {
                                            selectedSortOption = SortOption.NAME.name
                                            viewModel.saveSortBy(SortOption.NAME.name)
                                            expanded = false
                                        },
                                        text = {
                                            AppRow(
                                                modifier = Modifier.fillMaxWidth(),
                                                horizontalArrangement = Arrangement.SpaceBetween,
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                AppText(
                                                    text = stringResource(R.string.txtid_name),
                                                    fontSize = AppFontSize.BODY1,
                                                    fontWeight = FontWeight.W400,
                                                    lineHeight = 20.sp,
                                                    color = AppColors.current.titleText
                                                )
                                                AppSpacer(4.dp)

                                                if (selectedSortOption == SortOption.NAME.name) {
                                                    AppIcon(
                                                        R.drawable.svg_comm_ic_check,
                                                        size = 24.dp,
                                                        tint = AppColors.current.titleText,
                                                    )
                                                }
                                            }
                                        }
                                    )
                                    DropdownMenuItem(
                                        onClick = {
                                            selectedSortOption = SortOption.FAVORITES.name
                                            viewModel.saveSortBy(SortOption.FAVORITES.name)
                                            expanded = false
                                        },
                                        text = {
                                            AppRow(
                                                modifier = Modifier.fillMaxWidth(),
                                                horizontalArrangement = Arrangement.SpaceBetween,
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                AppText(
                                                    text = stringResource(R.string.txtid_favorites),
                                                    fontSize = AppFontSize.BODY1,
                                                    fontWeight = FontWeight.W400,
                                                    lineHeight = 20.sp,
                                                    color = AppColors.current.titleText
                                                )
                                                AppSpacer(4.dp)

                                                if (selectedSortOption == SortOption.FAVORITES.name) {
                                                    AppIcon(
                                                        R.drawable.svg_comm_ic_check,
                                                        size = 24.dp,
                                                        tint = AppColors.current.titleText,
                                                    )
                                                }
                                            }
                                        }
                                    )
                                }
                            }
                        }
                    }else{
                        AppDivider()
                        AppSpacer(8.dp)
                    }

                    val searchedText = textState.value.text.trim().lowercase()
                    val normalizedSearchText = StringUtils.normalizeVietnamese(searchedText).lowercase()
                    val formatter = DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.getDefault())

                    val sortedHistoryList = when (selectedSortOption) {
                        SortOption.DATE.name -> historyList.sortedByDescending { it.timestamp }
                        SortOption.NAME.name -> historyList.sortedBy {
                            it.historyName.lowercase(Locale.getDefault())
                        }
                        SortOption.FAVORITES.name -> historyList.sortedByDescending { it.isFavorite }
                        else -> historyList.sortedByDescending { it.timestamp }
                    }

                    val groupedHistory = when (selectedSortOption) {
                        SortOption.DATE.name -> sortedHistoryList
                            .filter {
                                val normalizedHistoryName = StringUtils.normalizeVietnamese(it.historyName.lowercase())
                                normalizedHistoryName.contains(normalizedSearchText) || it.historyName.lowercase().contains(searchedText)
                            }
                            .groupBy { it.timestamp.toLocalDate().format(formatter) }

                        SortOption.NAME.name -> sortedHistoryList
                            .filter {
                                val normalizedHistoryName = StringUtils.normalizeVietnamese(it.historyName.lowercase())
                                normalizedHistoryName.contains(normalizedSearchText) || it.historyName.lowercase().contains(searchedText)
                            }
                            .sortedBy { it.historyName.lowercase() }
                            .groupBy { it.historyName }

                        SortOption.FAVORITES.name -> sortedHistoryList
                            .filter {
                                val normalizedHistoryName = StringUtils.normalizeVietnamese(it.historyName.lowercase())
                                normalizedHistoryName.contains(normalizedSearchText) || it.historyName.lowercase().contains(searchedText)
                            }
                            .groupBy { it.isFavorite }

                        else -> sortedHistoryList
                            .filter {
                                val normalizedHistoryName = StringUtils.normalizeVietnamese(it.historyName.lowercase())
                                normalizedHistoryName.contains(normalizedSearchText) || it.historyName.lowercase().contains(searchedText)
                            }
                            .groupBy { it.timestamp.toLocalDate() }
                    }

                    val coroutineScope = rememberCoroutineScope()

                    var currentSwipeState: SwipeableState<Int>? by remember {
                        mutableStateOf(null)
                    }

                    val nestedScrollConnection = remember {
                        object : NestedScrollConnection {
                            override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                                if (currentSwipeState != null && currentSwipeState!!.currentValue != 0) {
                                    coroutineScope.launch {
                                        currentSwipeState!!.animateTo(0)
                                        currentSwipeState = null
                                    }
                                }
                                return Offset.Zero
                            }
                        }
                    }
                    LazyColumn(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxWidth()
                            .nestedScroll(nestedScrollConnection)
                            .padding(horizontal = 12.dp)
                    ) {
                        if (selectedSortOption == SortOption.FAVORITES.name) {
                            groupedHistory.forEach { (isFavorite, historyItems) ->
                                val title =
                                    if (isFavorite == true) context.getString(R.string.txtid_favorites) else context.getString(
                                        R.string.not_favorites
                                    )
                                item {
                                    AppSpacer(4.dp)
                                    AppText(
                                        text = title,
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(start = 16.dp),
                                        fontSize = AppFontSize.BODY2,
                                        lineHeight = 20.sp,
                                        fontWeight = FontWeight.W400,
                                        color = AppColors.current.titleContent
                                    )
                                }

                                items(items = historyItems, key = { it.historyId }) { chat ->
                                    AppRow(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                    ) {
                                        if (showMultiSelect) {
                                            Checkbox(
                                                modifier = Modifier.align(Alignment.CenterVertically),
                                                checked = selectedItems.contains(chat.historyId.toLong()),
                                                onCheckedChange = { isChecked ->
                                                    if (isChecked) {
                                                        selectedItems.add(chat.historyId.toLong())
                                                    } else {
                                                        selectedItems.remove(chat.historyId.toLong())
                                                    }
                                                },
                                                colors = CheckboxDefaults.colors(
                                                    checkedColor = AppColors.current.checkColor,
                                                    uncheckedColor = AppColors.current.unCheckColor
                                                ),
                                            )
                                        }
                                        HistoryItem(
                                            isEnableClick = !showMultiSelect,
                                            chat = chat,
                                            modifier = Modifier
                                                .weight(1f)
                                                .combinedClickable(
                                                    onClick = {
                                                        if (showMultiSelect) {
                                                            if (selectedItems.contains(chat.historyId.toLong())) {
                                                                selectedItems.remove(chat.historyId.toLong())
                                                            } else {
                                                                selectedItems.add(chat.historyId.toLong())
                                                            }
                                                        } else {
                                                            onClick(chat.historyId.toString())
                                                        }
                                                    },
                                                    onLongClick = {
                                                        showMultiSelect = !showMultiSelect
                                                        selectedItems.clear()
                                                        if (showMultiSelect) {
                                                            selectedItems.add(chat.historyId.toLong())
                                                        } else {
                                                            selectedItems.remove(chat.historyId.toLong())
                                                        }
                                                    }
                                                ),
                                            onEdit = { updatedChat ->
                                                viewModel.updateHistory(updatedChat)
                                            },
                                            onDelete = { chatToDelete ->
                                                viewModel.deleteHistory(chatToDelete)
                                            },
                                            onSwipeStateChanged = {
                                                if (it.targetValue == 0 && currentSwipeState == it) {
                                                    currentSwipeState = null
                                                    return@HistoryItem
                                                }
                                                // if there is no opening box, we set it to this opening one
                                                if (currentSwipeState == null) {
                                                    currentSwipeState = it
                                                } else {
                                                    // there already had one box opening, we need to swipe it back and then update the state to new one
                                                    coroutineScope.launch {
                                                        currentSwipeState!!.animateTo(0)
                                                        currentSwipeState = it
                                                    }
                                                }
                                            },
                                        )
                                    }
                                }
                            }
                        } else {
                            groupedHistory.forEach { (date, historyItems) ->
                                val displayDate =
                                    if (date == LocalDate.now().format(formatter)) context.getString(R.string.txtid_today)
                                        .uppercase()
                                    else date.toString()
                                if (selectedSortOption != SortOption.NAME.name) {
                                    item {
                                        AppSpacer(4.dp)
                                        AppText(
                                            text = displayDate,
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(start = 16.dp),
                                            fontSize = AppFontSize.BODY2,
                                            lineHeight = 20.sp,
                                            fontWeight = FontWeight.W400,
                                            color = AppColors.current.titleContent
                                        )
                                    }
                                }

                                items(items = historyItems, key = { it.historyId }) { chat ->
                                    AppRow(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                    ) {
                                        if (showMultiSelect) {
                                            Checkbox(
                                                modifier = Modifier.align(Alignment.CenterVertically),
                                                checked = selectedItems.contains(chat.historyId.toLong()),
                                                onCheckedChange = { isChecked ->
                                                    if (isChecked) {
                                                        selectedItems.add(chat.historyId.toLong())
                                                    } else {
                                                        selectedItems.remove(chat.historyId.toLong())
                                                    }
                                                },
                                                colors = CheckboxDefaults.colors(
                                                    checkedColor = AppColors.current.checkColor,
                                                    uncheckedColor = AppColors.current.unCheckColor
                                                ),
                                            )
                                        }
                                        HistoryItem(
                                            isEnableClick = !showMultiSelect,
                                            chat = chat,
                                            modifier = Modifier
                                                .weight(1f)
                                                .combinedClickable(
                                                    onClick = {
                                                        if (showMultiSelect) {
                                                            if (selectedItems.contains(chat.historyId.toLong())) {
                                                                selectedItems.remove(chat.historyId.toLong())
                                                            } else {
                                                                selectedItems.add(chat.historyId.toLong())
                                                            }
                                                        } else {
                                                            onClick(chat.historyId.toString())
                                                        }
                                                    },
                                                    onLongClick = {
                                                        showMultiSelect = !showMultiSelect
                                                        selectedItems.clear()
                                                        if (showMultiSelect) {
                                                            selectedItems.add(chat.historyId.toLong())
                                                        } else {
                                                            selectedItems.remove(chat.historyId.toLong())
                                                        }
                                                    }
                                                ),
                                            onEdit = { updatedChat ->
                                                viewModel.updateHistory(updatedChat)
                                            },
                                            onDelete = { chatToDelete ->
                                                viewModel.deleteHistory(chatToDelete)
                                            },
                                            onSwipeStateChanged = {
                                                if (it.targetValue == 0 && currentSwipeState == it) {
                                                    currentSwipeState = null
                                                    return@HistoryItem
                                                }
                                                // if there is no opening box, we set it to this opening one
                                                if (currentSwipeState == null) {
                                                    currentSwipeState = it
                                                } else {
                                                    // there already had one box opening, we need to swipe it back and then update the state to new one
                                                    coroutineScope.launch {
                                                        currentSwipeState!!.animateTo(0)
                                                        currentSwipeState = it
                                                    }
                                                }
                                            },
                                        )
                                    }
                                }
                            }
                        }
                    }
                    if (showMultiSelect) {
                        var showDeletesDialog by remember { mutableStateOf(false) }
                        AppRow(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showDeletesDialog = true
                                }
                                .background(AppColors.current.backgroundContent)
                                .padding(vertical = 12.dp),
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            AppColumn(
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                AppIcon(
                                    R.drawable.ic_history_trash,
                                    size = AppSize.ICON_SIZE,
                                    tint = AppColors.current.deleteColor,
                                )
                                AppText(
                                    text = stringResource(R.string.txtid_delete),
                                    fontSize = AppFontSize.BODY2,
                                    fontWeight = FontWeight.W400,
                                    lineHeight = 20.sp,
                                    color = AppColors.current.deleteColor,
                                )
                            }
                        }
                        if (showDeletesDialog) {
                            CommonDialog(
                                title = stringResource(R.string.delete_history),
                                message = stringResource(R.string.are_you_sure_you_want_to_delete) + "" + "?",
                                confirmText = stringResource(R.string.txtid_delete),
                                dismissText = stringResource(R.string.txtid_cancel),
                                onConfirm = {
                                    viewModel.deleteSelectedChats(selectedItems.toList())
                                    selectedItems.clear() // Xóa danh sách đã chọn
                                    showMultiSelect = false // Tắt chế độ chọn nhiều
                                },
                                onDismiss = {
                                    showDeletesDialog = false
                                }
                            )
                        }
                    }
                }
            } else {
                AppColumnCentered(
                    Modifier
                        .fillMaxSize()
                        .padding(bottom = innerPaddingHome.calculateBottomPadding()),
                ) {
                    Image(
                        modifier = Modifier.fillMaxWidth(0.8f),
                        painter = painterResource(R.drawable.svg_bg_no_chat),
                        contentDescription = null,
                    )
                    AppSpacer(32.dp)
                    AppText(
                        text = stringResource(R.string.no_conversations_here_yet),
                        fontSize = AppFontSize.BODY1,
                        color = AppColors.current.text,
                        lineHeight = 24.sp,
                        fontWeight = FontWeight.W700,
                    )
                    AppSpacer(4.dp)
                    AppText(
                        text = stringResource(R.string.try_asking_a_question_to_get_started),
                        fontSize = AppFontSize.BODY1,
                        color = AppColors.current.text,
                        lineHeight = 24.sp,
                        fontWeight = FontWeight.W400,
                    )
                    AppSpacer(64.dp)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun HistoryItem(
    isEnableClick: Boolean = true,
    chat: HistoryEntity,
    modifier: Modifier,
    onEdit: (HistoryEntity) -> Unit,
    onDelete: (HistoryEntity) -> Unit,
    onSwipeStateChanged: (SwipeableState<Int>) -> Unit = {},
) {
    var showEditDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }

    val coroutineScope = rememberCoroutineScope()
    val swipeableState = remember(chat.historyId) {
        SwipeableState(0)
    }
    SwipeBox(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clip(RoundedCornerShape(10.dp))
            .background(Color(0xFFE4F2FF)),
        state = swipeableState,
        swipeDirection = SwipeDirection.EndToStart,
        endContentWidth = if (!isEnableClick) 0.dp else 140.dp,
        endContent = { swipeableState, endSwipeProgress ->
            SwipeIcon(
                imageVector = ImageVector.vectorResource(R.drawable.ic_history_edit),
                contentDescription = "Edit",
                tint = Color(0xFF4BA1FF), //Color
                background = Color(0xFFE4F2FF),
                weight = 1f,
                iconSize = 24.dp
            ) {
                coroutineScope.launch {
                    swipeableState.animateTo(0)
                }
                showEditDialog = true
            }
            SwipeIcon(
                imageVector = ImageVector.vectorResource(R.drawable.ic_history_trash),
                contentDescription = "Delete",
                tint = Color.White,
                background = AppColors.current.deleteColor,
                weight = 1f,
                iconSize = 24.dp
            ) {
                coroutineScope.launch {
                    swipeableState.animateTo(0)
                }
                showDeleteDialog = true
            }
        }
    ) { _, _, _ ->
        // callback on parent when the state targetValue changes which means it is swiping to another state.
        LaunchedEffect(swipeableState.targetValue) {
            onSwipeStateChanged(swipeableState)
        }
        AppBox(modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(10.dp))
            .background(color = Color(0xFFE6C083)) //Color
        ) {
            AppColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 4.dp)
                    .clip(RoundedCornerShape(10.dp))
                    .background(color = AppColors.current.backgroundContent)
            ) {
                AppRow(
                    modifier = Modifier
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AppSpacer(12.dp)
                    AppColumn(
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = 0.dp, end = 0.dp, top = 12.dp, bottom = 12.dp),
                    ) {
                        AppRow(
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            if (chat.imageData != null) {
                                AppIcon(
                                    icon = R.drawable.ic_history_image,
                                    size = 20.dp,
                                    tint = AppColors.current.textHintColor,
                                )
                            }else{
                                AppIcon(
                                    icon = R.drawable.ic_history_messages,
                                    size = 20.dp,
                                    tint = AppColors.current.textHintColor,
                                )
                            }
                            AppSpacer(8.dp)
                            AppText(
                                text =
                                    if (chat.historyName.isEmpty())
                                        stringResource(R.string.txtid_no_title)
                                    else
                                        chat.historyName.capitaliseEachWord(),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                fontSize = AppFontSize.BODY2,
                                color = AppColors.current.text,
                                lineHeight = 20.sp,
                                fontWeight = FontWeight.W700
                            )
                        }
                        AppSpacer(8.dp)
                        AppRow(
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            AppGlideImage(
                                resId = when(chat.modelAiChat) {
                                    ModelAiMode.GEMINI.name -> R.drawable.ic_gemini
                                    ModelAiMode.GPT.name -> R.drawable.ic_chat_gpt
                                    else -> R.drawable.ic_chat_gpt
                                },
                                modifier = Modifier
                                    .size(20.dp)
                                    .clip(CircleShape),
                            )
                            AppSpacer(8.dp)
                            AppText(
                                modifier = Modifier.fillMaxWidth(),
                                text = chat.content,
                                maxLines = 1,
                                color = AppColors.current.itemHistoryHintText,
                                fontSize = AppFontSize.SMALL,
                                lineHeight = 16.sp,
                                fontWeight = FontWeight.W400,
                                overflow = TextOverflow.Ellipsis,
                            )
                        }

//                        AppText(
//                            text = convertTimestampToDateTime(chat.timestamp),
//                            fontSize = 10.sp,
//                            lineHeight = 12.sp,
//                            fontWeight = FontWeight.W400
//                        )
                    }
                    AppBox {
                        AppRow(
                            modifier = Modifier
                                .padding(8.dp)
                                .minWidth(AppSize.MIN_TOUCH_SIZE.times(1)),
                            horizontalArrangement = Arrangement.SpaceEvenly,
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            AppIcon(
                                icon = if (chat.isFavorite) R.drawable.ic_favorite else R.drawable.ic_history_no_favorite,
                                size = AppSize.ICON_SIZE,
                                clickZone = AppSize.MIN_TOUCH_SIZE,
                                tint = if (chat.isFavorite) AppColors.current.isFavoriteColor else AppColors.current.text,
                                onClick = if (!isEnableClick) null else {
                                    { onEdit(chat.copy(isFavorite = !chat.isFavorite)) }
                                }
                            )
                        }
                    }
                }
            }
        }
    }

    if (showEditDialog) {
        val textState = remember {
            mutableStateOf(TextFieldValue(chat.historyName))
        }
        CommonDialog(
            title = stringResource(R.string.edit_history_title),
            confirmText = stringResource(R.string.txtid_ok),
            dismissText = stringResource(R.string.txtid_cancel),
            textState = textState,
            isEditMode = true,
            onConfirm = {
                onEdit(chat.copy(historyName = textState.value.text))
                showEditDialog = false
            },
            onDismiss = { showEditDialog = false }
        )
    }
    if (showDeleteDialog) {
        CommonDialog(
            title = stringResource(R.string.delete_history),
            message = stringResource(R.string.are_you_sure_you_want_to_delete) + "" + "?",
            confirmText = stringResource(R.string.txtid_delete),
            dismissText = stringResource(R.string.txtid_cancel),
            onConfirm = {
                onDelete(chat)
                showDeleteDialog = false
            },
            onDismiss = {
                showDeleteDialog = false
            }
        )
    }
}