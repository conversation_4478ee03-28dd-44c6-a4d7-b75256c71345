<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="label_share_app" translate_by="gpt">Share app</string>
    <string name="txtid_writing" translate_by="gpt">Writing</string>
    <string name="txtid_light" translate_by="gpt">Light</string>
    <string name="txtid_dark" translate_by="gpt">Dark</string>
    <string name="txtid_system" translate_by="gpt">System</string>
    <string name="chat_bot" translate_by="gpt">Chat <PERSON><PERSON></string>
    <string name="picked_image" translate_by="gpt">Picked image</string>
    <string name="add_photo" translate_by="gpt">Add Photo</string>
    <string name="type_something" translate_by="gpt">Type something</string>
    <string name="send_prompt" translate_by="gpt">Send prompt</string>
    <string name="txtid_translate" translate_by="gpt">Translate</string>
    <string name="txtid_math" translate_by="gpt">Math</string>
    <string name="math_question" translate_by="gpt">Math Question</string>
    <string name="get_help_with" translate_by="gpt">Get Help With</string>
    <string name="geography" translate_by="gpt">Geography</string>
    <string name="chemistry" translate_by="gpt">Chemistry</string>
    <string name="the_account_has_been_deleted" translate_by="gemini">The account has been deleted</string>
    <string name="account_deletion_failed" translate_by="gpt">Account deletion failed</string>
    <string name="confirm_account_deletion_title" translate_by="gpt">Confirm account deletion</string>
    <string name="confirm_account_deletion_warning" translate_by="gpt">Are you sure you want to delete your account?</string>
    <string name="txtid_delete" translate_by="gpt">Delete</string>
    <string name="txtid_cancel" translate_by="gpt">Cancel</string>
    <string name="txtid_back" translate_by="gpt">Back</string>
    <string name="txtid_done" translate_by="gpt">Done</string>
    <string name="rotate_image" translate_by="gpt">Rotate Image</string>
    <string name="coming_soon" translate_by="gpt">Coming soon</string>
    <string name="arrow_down" translate_by="gpt">Arrow Down</string>
    <string name="solve_now_using_ai" translate_by="gpt">Solve now using AI</string>
    <string name="button_ai_photo" translate_by="gpt">Button AI photo</string>
    <string name="txtid_retry" translate_by="gpt">Retry</string>
    <string name="sign_in_successful" translate_by="gpt">Sign in successful</string>
    <string name="sign_up" translate_by="gpt">Sign up</string>
    <string name="txtid_login" translate_by="gpt">Log in</string>
    <string name="txtid_person" translate_by="gpt">Person</string>
    <string name="txtid_email" translate_by="gpt">Email</string>
    <string name="enter_email" translate_by="gemini">Enter email</string>
    <string name="password" translate_by="gpt">Password</string>
    <string name="enter_password" translate_by="gpt">Enter password</string>
    <string name="confirm_password" translate_by="gpt">Confirm password</string>
    <string name="re_enter_password" translate_by="gpt">Re-enter password</string>
    <string name="email_cannot_be_empty" translate_by="gpt">Email cannot be empty</string>
    <string name="password_cannot_be_empty" translate_by="gpt">Password cannot be empty</string>
    <string name="passwords_do_not_match" translate_by="gpt">Passwords do not match</string>
    <string name="sign_in" translate_by="gpt">Sign In</string>
    <string name="txtid_successful" translate_by="gpt">Successful</string>
    <string name="txtid_failed" translate_by="gpt">Failed:</string>
    <string name="already_have_an_account" translate_by="gemini">Already have an account?</string>
    <string name="don_t_have_an_account" translate_by="gpt">Don\'t have an account?</string>
    <string name="login_with_google" translate_by="gpt">Login with Google</string>
    <string name="txtid_ai_chat" translate_by="gpt">AI Chat</string>
    <string name="txtid_loading" translate_by="gpt">Loading…</string>
    <string name="txtid_history" translate_by="gpt">History</string>
    <string name="search_history" translate_by="gpt">Search history…</string>
    <string name="txtid_date" translate_by="gpt">Date</string>
    <string name="txtid_name" translate_by="gpt">Name</string>
    <string name="txtid_favorites" translate_by="gpt">Favourites</string>
    <string name="select_all" translate_by="gpt">Select all</string>
    <string name="not_favorites" translate_by="gpt">Not Favourites</string>
    <string name="txtid_today" translate_by="gpt">Today</string>
    <string name="no_history_available" translate_by="gpt">No history available</string>
    <string name="txtid_edit" translate_by="gpt">Edit</string>
    <string name="edit_history_title" translate_by="gpt">Edit History Title</string>
    <string name="txtid_title" translate_by="gpt">Title</string>
    <string name="txtid_save" translate_by="gpt">Save</string>
    <string name="delete_history" translate_by="gpt">Delete History</string>
    <string name="are_you_sure_you_want_to_delete" translate_by="gemini">Are you sure you want to delete</string>
    <string name="camera_access" translate_by="gpt">Camera access</string>
    <string name="camera_access_message" translate_by="gemini">We need to access the camera to scan your question. You can edit it anytime in Settings.</string>
    <string name="txtid_allow" translate_by="gpt">Allow</string>
    <string name="txtid_deny" translate_by="gpt">Deny</string>
    <string name="flash_is_not_supported_in_front_camera" translate_by="gpt">Flash is not supported on the front camera!</string>
    <string name="flash_toggled" translate_by="gpt">Flash toggled!</string>
    <string name="camera_permission_is_required" translate_by="gpt">Camera permission is required to use this feature.</string>
    <string name="go_to_settings" translate_by="gpt">Go to Settings</string>
    <string name="setting" translate_by="gpt">Settings</string>
    <string name="new_feature" translate_by="gpt">New Feature</string>
    <string name="subscribe_for_exclusive_benefit" translate_by="gpt">Subscribe for exclusive benefits</string>
    <string name="subscribe_now" translate_by="gpt">Subscribe now</string>
    <string name="txtid_settings" translate_by="gpt">Settings</string>
    <string name="select_ai_model" translate_by="gpt">Select AI Model</string>
    <string name="confirm_exit" translate_by="gpt">Confirm exit</string>
    <string name="are_you_sure_you_want_to_exit_the_app" translate_by="gpt">Are you sure you want to exit the app?</string>
    <string name="txtid_no" translate_by="gpt">No</string>
    <string name="learn_math_the_easy_way" translate_by="gpt">Learn Maths the Easy Way</string>
    <string name="accept_terms_here" translate_by="gpt">"By accessing or using EZ Math and its Services, you confirm that you understand and agree to our %1$s here."</string>
    <string name="terms_and_services" translate_by="gpt">Terms and Conditions</string>
    <string name="free_message_remain" translate_by="gemini">You have %1$d free messages left.</string>
    <string name="query_text_with_gemini" translate_by="gpt">Query text with Gemini</string>
    <string name="query_text_with_gpt" translate_by="gpt">Query text with Chat GPT</string>
    <string name="query_image_with_gemini" translate_by="gpt">Query image with Gemini</string>
    <string name="query_image_with_gpt" translate_by="gpt">Query image with Chat GPT</string>
    <string name="get_title" translate_by="gpt">Get title</string>
    <string name="selected" translate_by="gpt">Selected</string>
    <string name="txtid_theme" translate_by="gpt">Theme</string>
    <string name="ai_assistance" translate_by="gpt">AI Assistance</string>
    <string name="remove_ads" translate_by="gemini">Remove ads</string>
    <string name="txtid_share" translate_by="gpt">Share</string>
    <string name="txtid_feedback" translate_by="gpt">Feedback</string>
    <string name="txtid_rate_app" translate_by="gpt">Rate app</string>
    <string name="privacy_policy" translate_by="gpt">Privacy Policy</string>
    <string name="txtid_scan" translate_by="gpt">Scan</string>
    <string name="account_information" translate_by="gpt">Account Information</string>
    <string name="logged_out_successfully" translate_by="gpt">Logged out successfully</string>
    <string name="log_out" translate_by="gpt">Log out</string>
    <string name="delete_account" translate_by="gpt">Delete Account</string>
    <string name="crop_question_inside_frame" translate_by="gpt">Crop just one question inside the frame</string>
    <string name="translate_to" translate_by="gpt">Translate to</string>
    <string name="txtid_confirm" translate_by="gpt">Confirm</string>
    <string name="txtid_ok" translate_by="gpt">OK</string>
    <string name="restore_subscription" translate_by="gpt">Restore subscription</string>
    <string name="txtid_ai_chat_plus" translate_by="gpt">AI Chat Plus</string>
    <string name="access_advanced_features" translate_by="gpt">Access our most powerful model and advanced features</string>
    <string name="save_history" translate_by="gpt">Save History</string>
    <string name="mark_content" translate_by="gpt">Mark content</string>
    <string name="use_anytime" translate_by="gemini">Use anytime</string>
    <string name="new_stres_restore_pro_success" translate_by="gpt">Pro features have been restored successfully</string>
    <string name="new_stres_restore_pro_failed" translate_by="gpt">Restore purchase failed, please try again later</string>
    <string name="errors_please_try_again" translate_by="gemini">An error occurred, please try again</string>
    <string name="txtid_default" translate_by="gpt">Default</string>
    <string name="txtid_you" translate_by="gpt">You</string>
    <string name="txtid_version" translate_by="gpt">Version %1$s</string>
    <string name="dialog_need_account_title" translate_by="gpt">Sign In Required</string>
    <string name="dialog_need_account_body" translate_by="gemini">To use this feature, you need to sign in first.</string>
    <string name="log_in_again" translate_by="gpt">Log in again</string>
    <string name="log_in_to_delete_account" translate_by="gemini">Please log in again to delete your account</string>
    <string name="login_successful" translate_by="gpt">Login successful</string>
    <string name="purchase_successful" translate_by="gpt">Purchase successful</string>
    <string name="purchase_failed" translate_by="gpt">Purchase failed</string>
    <string name="txtid_calculator" translate_by="gpt">Calculator</string>
    <string name="txtid_close" translate_by="gpt">Close</string>
    <string name="txtid_choose_a_plan" translate_by="gpt">Choose a plan:</string>
    <string name="txtid_never_lose_important_chat" translate_by="gemini">Never lose important chats.</string>
    <string name="txtid_highlight_info" translate_by="gpt">Highlight and revisit key information.</string>
    <string name="txtid_24_7_access" translate_by="gpt">Enjoy 24/7 access.</string>
    <string name="txtid_no_hidden_fees" translate_by="gemini">No hidden fees</string>
    <string name="txtid_most_popular" translate_by="gpt">Most popular</string>
    <string name="txtid_free" translate_by="gpt">Free</string>
    <string name="txtid_no_title" translate_by="gpt">No title</string>
    <string name="enter_your_message_here" translate_by="gpt">Enter your message here…</string>
    <string name="write_a_message" translate_by="gpt">Write a message…</string>
    <string name="txtid_math_subtitle" translate_by="gpt">Solve maths problems easily</string>
    <string name="txtid_writing_subtitle" translate_by="gpt">Writing help and guidance</string>
    <string name="chemistry_subtitle" translate_by="gpt">Understand chemical reactions</string>
    <string name="geography_subtitle" translate_by="gpt">World knowledge</string>
    <string name="txtid_translate_subtitle" translate_by="gpt">Multiple languages</string>
    <string name="txtid_or" translate_by="gpt">Or:</string>
    <string name="reward_ad_not_ready" translate_by="gpt">Reward ad is not ready yet</string>
    <string name="txtid_insufficient_credits" translate_by="gpt">Insufficient Credits</string>
    <string name="txtid_get_credits" translate_by="gpt">Get Credits</string>
    <string name="txtid_watch_ad" translate_by="gemini">Watch Ad</string>
    <string name="txtid_watch_reward_ad" translate_by="gemini">Watch reward ad</string>
    <string name="go" translate_by="gpt">Go</string>
    <string name="feedback_and_suggestion" translate_by="gpt">Feedback and Suggestion</string>
    <string name="developer" translate_by="gpt">Developer</string>
    <string name="account" translate_by="gpt">Account</string>
    <string name="select_your_theme" translate_by="gpt">Select your theme</string>
    <string name="chat_gpt" translate_by="gpt">Chat GPT</string>
    <string name="smart_fast_creative" translate_by="gemini">Smart, fast, creative</string>
    <string name="germini" translate_by="gpt">Germini</string>
    <string name="deep_versatile_powerful" translate_by="gpt">Deep, versatile, powerful</string>
    <string name="other_subjects" translate_by="gpt">Other subjects</string>
    <string name="try_now" translate_by="gpt">Try now</string>
    <string name="feature_card_literature" translate_by="gpt">Literature</string>
    <string name="feature_card_write_an_essay" translate_by="gpt">Write an essay</string>
    <string name="feature_card_analysis_of_literary_works_stories_poems_plays" translate_by="gpt">Analysis of literary works  
(stories, poems, plays)</string>
    <string name="feature_card_let_ai_assist_you_from_start_to_finish" translate_by="gpt">Let AI assist you from start to finish!</string>
    <string name="feature_card_research_and_analysis" translate_by="gpt">Research and analysis</string>
    <string name="feature_card_find_evalute_interpret_and_visualize_information" translate_by="gpt">Find, evaluate, interpret and visualise information</string>
    <string name="hello" translate_by="gpt">Hello</string>
    <string name="how_can_i_assist_you_today" translate_by="gpt">How can I assist you today?</string>
    <string name="chat_history" translate_by="gpt">Chat history</string>
    <string name="ask_anything_get_yout_answer" translate_by="gpt">Ask anything, get your answer</string>
    <string name="copy" translate_by="gpt">Copy</string>
    <string name="deleting_your_account_will_erase_all_chat_history_personal_settings_and_related_data_this_action_cannot_be_undone" translate_by="gemini">Deleting your account will erase all chat history, personal settings, and related data. This action cannot be undone.</string>
    <string name="sure_you_want_to_delete_account" translate_by="gpt">Are you sure you want to delete your account?</string>
    <string name="top_up_coins" translate_by="gemini">Top-up coins</string>
    <string name="used_coins" translate_by="gpt">Used coins</string>
    <string name="days" translate_by="gpt">Days</string>
    <string name="crop_only_one_question_to_get_better_answer" translate_by="gemini">Crop to just one question for a better answer</string>
    <string name="recent_languages" translate_by="gpt">Recent Languages</string>
    <string name="all_languages" translate_by="gpt">All Languages</string>
    <string name="search_languages" translate_by="gpt">Search Languages</string>
    <string name="ok_got_it" translate_by="gemini">OK, Got it!</string>
    <string name="tap_here_to_pick_the_one_that_best_suits_your_task" translate_by="gpt">Tap here to choose the one that best suits your task</string>
    <string name="multiple_ai_models_available" translate_by="gpt">Multiple AI models available!</string>
    <string name="search" translate_by="gpt">Search</string>
    <string name="unselect_all" translate_by="gpt">Unselect all</string>
    <string name="select_language" translate_by="gpt">Select language</string>
    <string name="are_you_sure_you_want_to_log_out" translate_by="gpt">Are you sure you want to log out?</string>
    <string name="no_conversations_here_yet" translate_by="gpt">No conversations here yet!</string>
    <string name="try_asking_a_question_to_get_started" translate_by="gpt">Try asking a question to get started!</string>
    <string name="paragraph" translate_by="gpt">Paragraph</string>
    <string name="write_a_short_paragraph_about_the_importance_of_sleep" translate_by="gemini">Write a short paragraph about the importance of sleep.</string>
    <string name="email" translate_by="gpt">Email</string>
    <string name="help_me_write_a_professional_leave_request_email" translate_by="gpt">Help me write a professional leave request email.</string>
    <string name="intro" translate_by="gemini">Intro</string>
    <string name="write_an_introduction_paragraph_for_an_essay_on_climate_change" translate_by="gpt">Write an introductory paragraph for an essay on climate change.</string>
    <string name="equation" translate_by="gpt">Equation</string>
    <string name="help_me_solve_this_equation_2x_3_11" translate_by="gpt">Help me solve this equation: 2x + 3 = 11</string>
    <string name="derivative" translate_by="gpt">Derivative</string>
    <string name="show_me_how_to_find_the_derivative_of_f_x_x_sin_x" translate_by="gpt">Show me how to find the derivative of f(x) = x² · sin(x)</string>
    <string name="limit" translate_by="gpt">Limit</string>
    <string name="find_the_limit_of_sin_x_x_as_x_approaches_0" translate_by="gpt">Find the limit of sin(x)/x as x approaches 0</string>
    <string name="english" translate_by="gpt">English</string>
    <string name="translate_this_sentence_into_english_t_i_r_t_th_ch_h_c_ngo_i_ng" translate_by="gpt">I really enjoy learning foreign languages.</string>
    <string name="meaning" translate_by="gpt">Meaning</string>
    <string name="what_does_the_word_serendipity_mean_in_vietnamese" translate_by="gpt">What does the word \'serendipity\' mean in Vietnamese?</string>
    <string name="japanese" translate_by="gpt">Japanese</string>
    <string name="help_me_translate_this_paragraph_into_japanese" translate_by="gpt">Help me translate this paragraph into Japanese.</string>
    <string name="neutralize" translate_by="gpt">Neutralise</string>
    <string name="explain_what_an_acid_base_neutralization_reaction_is" translate_by="gpt">Explain what an acid-base neutralisation reaction is.</string>
    <string name="ethanol" translate_by="gpt">Ethanol</string>
    <string name="what_is_the_structural_formula_of_ethanol" translate_by="gpt">What is the structural formula of ethanol?</string>
    <string name="bonds" translate_by="gpt">Bonds</string>
    <string name="what_is_the_difference_between_ionic_and_covalent_bonds" translate_by="gpt">What is the difference between ionic and covalent bonds?</string>
    <string name="capital" translate_by="gpt">Capital</string>
    <string name="what_is_the_capital_of_brazil_what_s_special_about_it" translate_by="gemini">What is the capital city of Brazil? What\'s special about it?</string>
    <string name="compare" translate_by="gpt">Compare</string>
    <string name="compare_the_geography_of_asia_and_africa" translate_by="gpt">Compare the geography of Asia and Africa.</string>
    <string name="desert" translate_by="gpt">Desert</string>
    <string name="why_is_the_sahara_the_largest_desert_in_the_world" translate_by="gpt">Why is the Sahara the largest desert in the world?</string>
    <string name="start" translate_by="gpt">Start</string>
    <string name="hi_what_can_you_help_me_with_today" translate_by="gemini">Hi! How can I help you today?</string>
    <string name="explain" translate_by="gpt">Explain</string>
    <string name="can_you_explain_how_this_app_works" translate_by="gpt">Can you explain how this app works?</string>
    <string name="tips" translate_by="gpt">Tips</string>
    <string name="give_me_tips_for_getting_the_best_results" translate_by="gemini">Give me tips for getting the best results.</string>
    <string name="exit_app" translate_by="gpt">Exit App</string>
    <string name="do_you_want_to_leave_now_don_t_forget_to_come_back_i_ll_be_waiting_for_you" translate_by="gpt">Do you want to leave now? Don\'t forget to come back, I\'ll be waiting for you!</string>
    <string name="gemini" translate_by="gpt">Gemini</string>
    <string name="describe_place" translate_by="gemini">A Peaceful Place</string>
    <string name="describe_a_place_that_makes_you_feel_at_peace" translate_by="gemini">Describe a place that makes you feel at peace</string>
    <string name="favorite_memory" translate_by="gpt">Favourite Childhood Memory</string>
    <string name="describe_your_favorite_childhood_memory" translate_by="gpt">Describe your favourite childhood memory</string>
    <string name="inspiring_event" translate_by="gpt">Life-Changing Event</string>
    <string name="describe_an_event_that_changed_your_life" translate_by="gpt">Describe an event that changed your life</string>
    <string name="argumentative" translate_by="gpt">Argumentative Essay</string>
    <string name="argumentative_essay_example" translate_by="gemini">Write an argumentative essay about school uniform</string>
    <string name="narrative" translate_by="gpt">Narrative Essay</string>
    <string name="narrative_essay_example" translate_by="gpt">Write a narrative essay about overcoming a challenge</string>
    <string name="expository" translate_by="gpt">Expository Essay</string>
    <string name="expository_essay_example" translate_by="gemini">Write an expository essay explaining the water cycle</string>
    <string name="word_300" translate_by="gpt">300 Words</string>
    <string name="write_essay_300_words" translate_by="gpt">Write a 300-word essay on this topic</string>
    <string name="word_500" translate_by="gpt">500 Words</string>
    <string name="write_essay_500_words" translate_by="gpt">Write a 500-word essay on this topic</string>
    <string name="word_1000" translate_by="gpt">1000 Words</string>
    <string name="write_essay_1000_words" translate_by="gpt">Write a 1000-word essay on this topic</string>
    <string name="formal" translate_by="gpt">Formal Tone</string>
    <string name="write_formally_about_topic" translate_by="gpt">Write formally about this topic</string>
    <string name="academic" translate_by="gpt">Academic Tone</string>
    <string name="write_academically_about_topic" translate_by="gpt">Write in an academic tone for this topic</string>
    <string name="informal" translate_by="gpt">Informal / Blog Style</string>
    <string name="write_informally_like_a_blog_post" translate_by="gpt">Write informally like a blog post</string>
    <string name="the_great_gatsby" translate_by="gpt">The Great Gatsby</string>
    <string name="write_about_the_great_gatsby" translate_by="gpt">Write about "The Great Gatsby"</string>
    <string name="to_kill_a_mockingbird" translate_by="gpt">To Kill a Mockingbird</string>
    <string name="write_about_to_kill_a_mockingbird" translate_by="gpt">Write about "To Kill a Mockingbird"</string>
    <string name="romeo_and_juliet" translate_by="gpt">Romeo and Juliet</string>
    <string name="write_about_romeo_and_juliet" translate_by="gpt">Write about "Romeo and Juliet"</string>
    <string name="f_scott_fitzgerald" translate_by="gpt">F. Scott Fitzgerald</string>
    <string name="write_about_f_scott_fitzgerald" translate_by="gpt">Write about the author F. Scott Fitzgerald</string>
    <string name="harper_lee" translate_by="gpt">Harper Lee</string>
    <string name="write_about_harper_lee" translate_by="gpt">Write about the author Harper Lee</string>
    <string name="william_shakespeare" translate_by="gpt">William Shakespeare</string>
    <string name="write_about_william_shakespeare" translate_by="gpt">Write about the author William Shakespeare</string>
    <string name="character_analysis" translate_by="gpt">Character Analysis</string>
    <string name="do_character_analysis" translate_by="gpt">Do a character analysis of the main character</string>
    <string name="main_themes" translate_by="gpt">Main Themes</string>
    <string name="analyze_main_themes" translate_by="gpt">Analyse the main themes in the story</string>
    <string name="symbolism" translate_by="gpt">Symbolism</string>
    <string name="explain_the_symbolism_in_the_work" translate_by="gpt">Explain the symbolism used in the work</string>
    <string name="length_300" translate_by="gpt">300-word Essay</string>
    <string name="write_300_words_on_topic" translate_by="gpt">Write a 300-word response on the topic</string>
    <string name="length_500" translate_by="gpt">500-word Essay</string>
    <string name="write_500_words_on_topic" translate_by="gpt">Write a 500-word response on the topic</string>
    <string name="length_1000" translate_by="gpt">1000-word Essay</string>
    <string name="write_1000_words_on_topic" translate_by="gpt">Write a 1000-word response on the topic</string>
    <string name="middle_school" translate_by="gpt">Secondary School</string>
    <string name="write_for_middle_school" translate_by="gpt">Write suitable for secondary school students</string>
    <string name="high_school" translate_by="gpt">Secondary School</string>
    <string name="write_for_high_school" translate_by="gpt">Write suitable for secondary school students</string>
    <string name="university" translate_by="gpt">University</string>
    <string name="write_for_university" translate_by="gpt">Write suitable for university students</string>
    <string name="climate_change" translate_by="gpt">Climate Change</string>
    <string name="research_on_climate_change" translate_by="gpt">Research on climate change and its impact</string>
    <string name="ai_on_jobs" translate_by="gpt">AI and Jobs</string>
    <string name="research_on_ai_and_jobs" translate_by="gpt">Research on the impact of AI on jobs</string>
    <string name="social_media" translate_by="gpt">Social Media and Young People</string>
    <string name="research_on_social_media_effects" translate_by="gpt">Research on the effects of social media on young people</string>
    <string name="info_gathering" translate_by="gpt">Information Gathering</string>
    <string name="goal_information_gathering" translate_by="gpt">Goal: Gather general information on the topic</string>
    <string name="trend_analysis" translate_by="gpt">Trend Analysis</string>
    <string name="goal_trend_analysis" translate_by="gpt">Goal: Analyse trends over the last decade</string>
    <string name="problem_solution" translate_by="gpt">Problem &amp; Solution</string>
    <string name="goal_problem_and_solution" translate_by="gemini">Goal: Identify a problem and propose a solution</string>
    <string name="scientific_journals" translate_by="gpt">Scientific Journals</string>
    <string name="use_scientific_journals" translate_by="gpt">Use scientific journals as primary sources</string>
    <string name="official_articles" translate_by="gpt">Official Articles</string>
    <string name="use_official_articles" translate_by="gpt">Use official articles and reports as references</string>
    <string name="books" translate_by="gpt">Books</string>
    <string name="use_books_as_references" translate_by="gemini">Use books as the main source of information</string>
    <string name="high_school_students" translate_by="gpt">Secondary School Students</string>
    <string name="research_for_high_school" translate_by="gpt">Make it suitable for high school-level research</string>
    <string name="college_students" translate_by="gpt">University Students</string>
    <string name="research_for_college_students" translate_by="gpt">Make it suitable for university-level research</string>
    <string name="advanced_research" translate_by="gpt">Advanced Research</string>
    <string name="research_for_advanced_level" translate_by="gpt">Make it suitable for advanced academic research.</string>
    <string name="onboarding_title_1" translate_by="gpt">EZ Maths</string>
    <string name="onboarding_desc_1" translate_by="gpt">AI app that solves Maths problems quickly, accurately, and clearly, making tough questions easy.</string>
    <string name="onboarding_title_2" translate_by="gpt">Snap &amp; Solve</string>
    <string name="onboarding_desc_2" translate_by="gpt">Homework stress? Not anymore. Take a photo, get the answer, and learn smarter every day!</string>
    <string name="onboarding_title_3" translate_by="gpt">Multi-AI Response</string>
    <string name="onboarding_desc_3" translate_by="gemini">Every question opens a door — and multiple AIs offer unique answers to guide your way.</string>
    <string name="skip" translate_by="gpt">Skip</string>
    <string name="next" translate_by="gpt">Next</string>
    <string name="get_started" translate_by="gpt">Get started</string>
    <string name="copied_text" translate_by="gpt">Copied Text</string>
    <string name="copied_to_clipboard" translate_by="gpt">Copied to clipboard</string>
    <string name="nothing_to_copy" translate_by="gpt">Nothing to copy</string>
    <string name="content_splash" translate_by="gpt">Powered by AI. Driven by you</string>
    <string name="required_fields_message" translate_by="gemini">Please enter required fields: %1$s</string>
    <string name="ok" translate_by="gpt">OK</string>
</resources>