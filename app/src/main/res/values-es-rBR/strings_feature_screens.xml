<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gemini">Gerar estrutura de tópicos</string>
    <string name="placeholder_default" translate_by="gemini">Digite seu texto aqui…</string>
    <string name="essay_screen_description" translate_by="gpt">¡Claro! Puedo ayudar con eso. Por favor, proporciona algunos detalles para tu ensayo a continuación.</string>
    <string name="label_choose_topic" translate_by="gpt">Elige un tema</string>
    <string name="label_essay_type" translate_by="gpt">Tipo de ensayo</string>
    <string name="label_word_count" translate_by="gpt">Recuento de palabras</string>
    <string name="label_language_tone" translate_by="gpt">Idioma + tono</string>
    <string name="placeholder_topic" translate_by="gpt">Ej: Describe un lugar que te haga sentir en paz</string>
    <string name="placeholder_essay_type" translate_by="gemini">Arg<PERSON>nta<PERSON><PERSON>, Narrativo…</string>
    <string name="placeholder_word_count" translate_by="google">Ej.: 300 palabras, 500 palabras, 1000 palabras …</string>
    <string name="placeholder_language_tone" translate_by="gpt">Ex: Formal, acadêmico, …</string>
    <string name="research_screen_description" translate_by="gemini">Um lugar para transformar dados brutos em histórias visuais significativas por meio de pesquisa e análise.</string>
    <string name="label_research_topic" translate_by="gpt">Tema de investigación</string>
    <string name="label_research_goal" translate_by="gemini">Objetivo da pesquisa</string>
    <string name="label_preferred_sources" translate_by="gpt">Fuentes preferidas</string>
    <string name="label_depth_length" translate_by="gpt">Profundidad / Longitud</string>
    <string name="label_academic_level" translate_by="gpt">Nivel académico</string>
    <string name="placeholder_research_topic" translate_by="google">Ej: cambio climático, el impacto de la IA en los trabajos, …</string>
    <string name="placeholder_research_goal" translate_by="google">Ej: recopilación de información, análisis de tendencias …</string>
    <string name="placeholder_preferred_sources" translate_by="gemini">Revistas científicas, livros, artigos oficiais</string>
    <string name="placeholder_depth_length" translate_by="google">Ej.: 300 palabras, 500 palabras, 1000 palabras …</string>
    <string name="placeholder_academic_level" translate_by="gemini">Estudantes do ensino médio, universitários, pesquisa avançada, …</string>
    <string name="literature_screen_description" translate_by="gemini">Das palavras aos significados ocultos, ajudamos você a descobrir o verdadeiro valor de cada obra literária.</string>
    <string name="label_title_of_work" translate_by="gemini">Título da obra</string>
    <string name="label_author" translate_by="gpt">Autor</string>
    <string name="label_analysis_type" translate_by="gpt">¿Qué quieres analizar?</string>
    <string name="label_format" translate_by="gpt">Longitud / formato</string>
    <string name="placeholder_title" translate_by="gemini">O Grande Gatsby</string>
    <string name="placeholder_author" translate_by="google">Ej: F. Scott Fitzgerald</string>
    <string name="placeholder_analysis_type" translate_by="google">Ej: Análisis de personajes, temas principales …</string>
    <string name="placeholder_format" translate_by="google">Ej.: 300 palabras, 500 palabras, 1000 palabras …</string>
    <string name="placeholder_academic_level_literature" translate_by="gemini">Ensino fundamental, ensino médio ou universidade, …</string>
    <string name="research_outline_topic_label" translate_by="gemini">📘 Tópico de pesquisa: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gemini">🎯 Objetivo da pesquisa: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 Fontes Preferidas: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gemini">📏 Profundidade/Comprimento: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gemini">🎓 Nível Acadêmico: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gpt">🧾 Esquema Sugerido:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. Introducción</string>
    <string name="research_outline_introduction_overview" translate_by="gemini">Breve visão geral de %1$s</string>
    <string name="research_outline_introduction_importance" translate_by="gemini">Importância da pesquisa no nível de %1$s</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. Objetivos</string>
    <string name="research_outline_objectives_goal" translate_by="gpt">- Aclare el objetivo principal: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. Metodología</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">- Enfoque de investigación</string>
    <string name="research_outline_methodology_sources" translate_by="gemini">Fontes de dados: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gemini">4. Principais Insights</string>
    <string name="research_outline_key_insights_trends" translate_by="gpt">- Discutir tendencias, hechos o hallazgos de análisis</string>
    <string name="research_outline_key_insights_citations" translate_by="gpt">- Use citas si es necesario</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. Conclusión</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">- Resumen de hallazgos</string>
    <string name="research_outline_conclusion_implications" translate_by="gpt">- Implicaciones o trabajo futuro</string>
    <string name="essay_outline_topic_label" translate_by="gemini">✏️ Tópico do Ensaio: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gemini">📝 Tipo de redação: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gemini">🔢 Contagem de palavras: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ Idioma y Tono: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gpt">🧾 Esquema Sugerido:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. Introducción</string>
    <string name="essay_outline_introduction_topic" translate_by="gemini">Apresentar o tópico: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- Proporcionar antecedentes/contexto</string>
    <string name="essay_outline_introduction_thesis" translate_by="google">- Indique la tesis</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. Párrafos del Cuerpo</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- Párrafo 1: Primer argumento o punto</string>
    <string name="essay_outline_body_paragraph2" translate_by="gemini">Parágrafo 2: Evidências de apoio ou narrativa</string>
    <string name="essay_outline_body_paragraph3" translate_by="gemini">Parágrafo 3: Contra-argumento ou detalhe adicional</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. Conclusión</string>
    <string name="essay_outline_conclusion_summary" translate_by="gemini">Resumir pontos principais</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">- Reformule la tesis de una nueva manera</string>
    <string name="essay_outline_conclusion_final" translate_by="gemini">Conclua com uma reflexão final impactante</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ Notas:</string>
    <string name="essay_outline_notes_tone" translate_by="gemini">Mantenha um tom de %1$s consistente.</string>
    <string name="essay_outline_notes_wordcount" translate_by="gemini">Tente atingir aproximadamente %1$s no total</string>
    <string name="essay_outline_notes_structure" translate_by="gemini">Siga a estrutura típica de redação de %1$s</string>
    <string name="literature_outline_title_label" translate_by="gpt">Título: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Autor: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gemini">Foco: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gemini">Comprimento: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gemini">Nível Acadêmico: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gpt">Esquema:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. Introducción</string>
    <string name="literature_outline_introduction_context" translate_by="gemini">Apresente a obra literária e seu contexto.</string>
    <string name="literature_outline_introduction_author" translate_by="gpt">Mencione el autor y la relevancia para el enfoque de análisis elegido.</string>
    <string name="literature_outline_background_title" translate_by="gemini">2. Plano de Fundo</string>
    <string name="literature_outline_background_summary" translate_by="gemini">Resumo do enredo ou personagens principais (dependendo do tipo de análise).</string>
    <string name="literature_outline_background_context" translate_by="google">Proporcionar el contexto necesario para un análisis más profundo.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. Análisis Principal</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gemini">Mergulhe fundo em: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">Utiliza evidencia del texto: citas, eventos, simbolismo, etc.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. Conexões</string>
    <string name="literature_outline_connections_themes" translate_by="gemini">Vincular a análise a temas maiores ou implicações do mundo real.</string>
    <string name="literature_outline_connections_contrast" translate_by="gpt">Opcionalmente, contrasta con otros personajes u obras.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. Conclusión</string>
    <string name="literature_outline_conclusion_insights" translate_by="gemini">Reafirme os principais insights.</string>
    <string name="literature_outline_conclusion_value" translate_by="gpt">Reflexione sobre el valor del trabajo desde una perspectiva académica.</string>
</resources>