package amobi.module.common.advertisements.banner_ad

import amobi.module.common.advertisements.AdvertsConfig
import amobi.module.common.views.CommActivity
import android.view.View
import android.view.ViewGroup
import com.google.android.gms.ads.AdSize
import java.lang.ref.WeakReference

object AdvertsManagerBanner {
    private var mBanner: AdvertsInstanceBanner? = null

    fun requestArrayRectBannerAds(
        bannerAdArray: ArrayList<AdvertsInstanceBanner>,
        size: Int,
        listAdsID: Array<String>,
        adSize: AdSize = AdSize.MEDIUM_RECTANGLE,
    ) {
        if (AdvertsConfig.instance.isHideAd) return
        for (i in 0 until size) {
            val bannerAd = bannerAdArray.getOrNull(i)
            if (bannerAd?.isAdvertsAvailable == true || bannerAd?.isAdvertsLoading == true) continue

            val adView = AdvertsInstanceBanner(listAdsID)
            if (bannerAd == null) {
                bannerAdArray.add(adView)
            } else {
                bannerAdArray[i] = adView
            }
            adView.requestBannerAdverts(adSize = adSize)
        }
    }

    fun popShowArrayRectBannerAds(
        container: ViewGroup?,
        placeHolderContainer: View?,
        bannerAdArray: ArrayList<AdvertsInstanceBanner>,
        listAdsID: Array<String>,
        adSize: AdSize = AdSize.MEDIUM_RECTANGLE,
        fillCache: Boolean = true,
        adTag: String = "",
    ): AdvertsInstanceBanner? {
        if (AdvertsConfig.instance.isHideAd) return null
        if (container == null) return null

        val banner = bannerAdArray.getOrNull(0)
        if (banner?.isAdvertsAvailable == true) {
            banner.showBannerAdverts(container, placeHolderContainer)
            if (fillCache) {
                val newAdInstance = AdvertsInstanceBanner(listAdsID, adTag = adTag)
                if (bannerAdArray.getOrNull(0) == null) {
                    bannerAdArray.add(newAdInstance)
                } else {
                    bannerAdArray[0] = newAdInstance
                }
                newAdInstance.requestBannerAdverts(adSize = adSize)
            }
            return banner
        } else {
            val adView = AdvertsInstanceBanner(listAdsID, adTag = adTag)
            val weakRefContainer = WeakReference(container)
            adView.requestBannerAdverts(
                onAdLoadedListener = {
                    val containerNotNull = weakRefContainer.get() ?: return@requestBannerAdverts Unit
                    adView.showBannerAdverts(containerNotNull, placeHolderContainer)
                    if (!fillCache) return@requestBannerAdverts Unit
                    val newBanner = bannerAdArray.getOrNull(0)
                    if (newBanner?.isAdvertsAvailable == true || newBanner?.isAdvertsLoading == true)
                        return@requestBannerAdverts Unit
                    val newAdInstance = AdvertsInstanceBanner(listAdsID, adTag = adTag)
                    if (bannerAdArray.getOrNull(0) == null) {
                        bannerAdArray.add(newAdInstance)
                    } else {
                        bannerAdArray[0] = newAdInstance
                    }
                    newAdInstance.requestBannerAdverts(adSize = adSize)
                },
                adSize = adSize,
            )
            return adView
        }
    }

    fun requestBannerAds(
        listAdsID: Array<String>,
        activityCollapsible: CommActivity? = null,
        activity: CommActivity? = null,
        onAdLoadedListener: (() -> Unit?)? = null,
    ) {
        if (AdvertsConfig.instance.isHideAd) return
        if (mBanner?.isAdvertsAvailable == true || mBanner?.isAdvertsLoading == true) return
        mBanner = AdvertsInstanceBanner(listAdsID)
        mBanner?.requestBannerAdverts(
            onAdLoadedListener = onAdLoadedListener,
            activityCollapsible = activityCollapsible,
            activity = activity,
        )
    }

    fun isBannerAdvertsAvailable(): Boolean {
        if (AdvertsConfig.instance.isHideAd) return false
        return mBanner?.isAdvertsAvailable == true
    }

    fun isBannerAdvertsLoadTried(): Boolean {
        if (AdvertsConfig.instance.isHideAd) return false
        val banner = mBanner ?: return false
        if (mBanner?.isAdvertsLoading == true) return true
        if (banner.admobErrorCode != null) return true
        return false
    }

    fun showAdverts(
        container: ViewGroup?,
        placeHolderContainer: View? = null,
    ) {
        if (container == null) return
        mBanner?.showBannerAdverts(container, placeHolderContainer)
    }
}
